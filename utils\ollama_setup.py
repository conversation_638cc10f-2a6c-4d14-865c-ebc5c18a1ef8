"""
Ollama setup and validation utilities
"""

import subprocess
import platform
import requests
import time
import webbrowser
import json
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import show_info, show_error, confirm_action


class OllamaSetupWizard(LoggerMixin):
    """Wizard to help users set up Ollama properly"""

    def __init__(self, config: Config):
        self.config = config
        self.base_url = config.get("ollama.base_url", "http://localhost:11434")

    def run_setup_wizard(self) -> bool:
        """Run the complete setup wizard"""
        self.logger.info("Starting Ollama setup wizard")

        try:
            # Step 1: Check if Ollama is installed
            if not self._check_ollama_installation():
                if not self._guide_ollama_installation():
                    return False

            # Step 2: Check if service is running
            if not self._check_service_running():
                if not self._start_ollama_service():
                    return False

            # Step 3: Validate connection
            if not self._validate_connection():
                return False

            # Step 4: Check/install default model
            if not self._setup_default_model():
                return False

            # Step 5: Run final validation
            return self._run_final_validation()

        except Exception as e:
            self.logger.error(f"Setup wizard failed: {e}")
            show_error("Setup Failed", f"Setup wizard encountered an error: {str(e)}")
            return False

    def _check_ollama_installation(self) -> bool:
        """Check if Ollama is installed"""
        try:
            result = subprocess.run(
                ["ollama", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                version = result.stdout.strip()
                self.logger.info(f"Ollama is installed: {version}")
                show_info("Ollama Found", f"Ollama is installed: {version}")
                return True
            else:
                return False

        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            self.logger.warning("Ollama is not installed or not in PATH")
            return False

    def _guide_ollama_installation(self) -> bool:
        """Guide user through Ollama installation"""
        system = platform.system().lower()

        install_instructions = {
            "windows": {
                "message": "Ollama is not installed. Please download and install it from the official website.",
                "url": "https://ollama.ai/download/windows",
                "steps": [
                    "1. Click 'Open Download Page' to go to the Ollama website",
                    "2. Download the Windows installer",
                    "3. Run the installer and follow the instructions",
                    "4. Restart this application after installation"
                ]
            },
            "darwin": {  # macOS
                "message": "Ollama is not installed. Please download and install it from the official website.",
                "url": "https://ollama.ai/download/mac",
                "steps": [
                    "1. Click 'Open Download Page' to go to the Ollama website",
                    "2. Download the macOS installer",
                    "3. Run the installer and follow the instructions",
                    "4. Restart this application after installation"
                ]
            },
            "linux": {
                "message": "Ollama is not installed. You can install it using the official install script.",
                "url": "https://ollama.ai/download/linux",
                "steps": [
                    "1. Open a terminal",
                    "2. Run: curl -fsSL https://ollama.ai/install.sh | sh",
                    "3. Wait for installation to complete",
                    "4. Restart this application after installation"
                ]
            }
        }

        instructions = install_instructions.get(system, install_instructions["linux"])

        message = f"{instructions['message']}\n\n" + "\n".join(instructions['steps'])

        if confirm_action("Install Ollama", message + "\n\nWould you like to open the download page?"):
            try:
                webbrowser.open(instructions['url'])
            except Exception as e:
                self.logger.error(f"Failed to open browser: {e}")

        show_info("Installation Required",
                 "Please install Ollama and restart the application to continue.")
        return False

    def _check_service_running(self) -> bool:
        """Check if Ollama service is running"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.logger.info("Ollama service is running")
                return True
            else:
                self.logger.warning(f"Ollama service returned status {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.logger.info("Ollama service is not running")
            return False
        except Exception as e:
            self.logger.error(f"Error checking Ollama service: {e}")
            return False

    def _start_ollama_service(self) -> bool:
        """Attempt to start Ollama service"""
        if not confirm_action("Start Ollama Service",
                             "Ollama service is not running. Would you like to start it?"):
            return False

        try:
            self.logger.info("Starting Ollama service")

            if platform.system() == "Windows":
                # On Windows, try to start as a background process
                subprocess.Popen(
                    ["ollama", "serve"],
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:
                # On Unix-like systems
                subprocess.Popen(
                    ["ollama", "serve"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )

            # Wait for service to start
            show_info("Starting Service", "Starting Ollama service, please wait...")

            for attempt in range(10):  # Wait up to 10 seconds
                time.sleep(1)
                if self._check_service_running():
                    show_info("Service Started", "Ollama service started successfully!")
                    return True

            show_error("Service Start Failed",
                      "Could not start Ollama service automatically. Please start it manually.")
            return False

        except Exception as e:
            self.logger.error(f"Failed to start Ollama service: {e}")
            show_error("Service Start Error", f"Error starting Ollama service: {str(e)}")
            return False

    def _validate_connection(self) -> bool:
        """Validate connection to Ollama service"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()

            data = response.json()
            model_count = len(data.get("models", []))

            self.logger.info(f"Connection validated. {model_count} models available.")
            show_info("Connection Validated",
                     f"Successfully connected to Ollama service. {model_count} models available.")
            return True

        except Exception as e:
            self.logger.error(f"Connection validation failed: {e}")
            show_error("Connection Failed",
                      f"Could not connect to Ollama service: {str(e)}")
            return False

    def _setup_default_model(self) -> bool:
        """Setup default model if not available"""
        default_model = self.config.get("ollama.default_model", "llama2")

        try:
            # Check if default model is available
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()

            data = response.json()
            available_models = [model["name"] for model in data.get("models", [])]

            if default_model in available_models:
                self.logger.info(f"Default model '{default_model}' is available")
                return True

            # Model not available, offer to download
            if confirm_action("Download Model",
                             f"Default model '{default_model}' is not available. Would you like to download it?\n\n"
                             f"This may take several minutes depending on your internet connection."):
                return self._download_model(default_model)
            else:
                # Offer alternative models
                if available_models:
                    show_info("Alternative Models",
                             f"Available models: {', '.join(available_models)}\n\n"
                             f"You can change the default model in settings.")
                    return True
                else:
                    show_error("No Models", "No models are available. Please download a model manually.")
                    return False

        except Exception as e:
            self.logger.error(f"Error checking default model: {e}")
            show_error("Model Check Failed", f"Could not check model availability: {str(e)}")
            return False

    def _download_model(self, model_name: str) -> bool:
        """Download a model with progress feedback"""
        try:
            self.logger.info(f"Downloading model: {model_name}")
            show_info("Downloading Model", f"Downloading {model_name}... This may take several minutes.")

            # Start model download
            response = requests.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                stream=True,
                timeout=600  # 10 minutes timeout
            )
            response.raise_for_status()

            # Process streaming response
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))

                        # Log progress
                        if "status" in data:
                            self.logger.debug(f"Download status: {data['status']}")

                        # Check for completion
                        if data.get("status") == "success":
                            show_info("Download Complete", f"Model '{model_name}' downloaded successfully!")
                            return True

                        # Check for errors
                        if "error" in data:
                            raise Exception(data["error"])

                    except json.JSONDecodeError:
                        continue

            return True

        except Exception as e:
            self.logger.error(f"Failed to download model {model_name}: {e}")
            show_error("Download Failed", f"Failed to download model: {str(e)}")
            return False

    def _run_final_validation(self) -> bool:
        """Run final validation of the setup"""
        try:
            # Test basic functionality
            from analysis.ollama_client import OllamaClient

            client = OllamaClient(self.config)

            # Test connection
            if not client.is_available():
                show_error("Validation Failed", "Ollama service is not available")
                return False

            # Test model listing
            models = client.get_models()
            if not models:
                show_error("Validation Failed", "No models are available")
                return False

            # Test basic generation (optional)
            if confirm_action("Test Generation",
                             "Would you like to test text generation? This will send a simple test prompt."):
                try:
                    result = client.generate("Say hello", max_tokens=10)
                    if result:
                        show_info("Test Successful", f"Generation test successful! Response: {result[:100]}...")
                    else:
                        show_error("Test Failed", "Generation test failed")
                        return False
                except Exception as e:
                    show_error("Test Failed", f"Generation test failed: {str(e)}")
                    return False

            show_info("Setup Complete",
                     f"Ollama setup completed successfully!\n\n"
                     f"Available models: {', '.join(models[:3])}{'...' if len(models) > 3 else ''}")
            return True

        except Exception as e:
            self.logger.error(f"Final validation failed: {e}")
            show_error("Validation Failed", f"Setup validation failed: {str(e)}")
            return False

    def get_system_info(self) -> Dict[str, Any]:
        """Get system information for troubleshooting"""
        info = {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "python_version": platform.python_version(),
            "ollama_installed": self._check_ollama_installation(),
            "service_running": self._check_service_running(),
            "base_url": self.base_url
        }

        # Try to get Ollama version
        try:
            result = subprocess.run(
                ["ollama", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                info["ollama_version"] = result.stdout.strip()
        except:
            info["ollama_version"] = "Unknown"

        # Try to get available models
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                info["available_models"] = [model["name"] for model in data.get("models", [])]
            else:
                info["available_models"] = []
        except:
            info["available_models"] = []

        return info


def run_ollama_setup_wizard(config: Config) -> bool:
    """Convenience function to run the setup wizard"""
    wizard = OllamaSetupWizard(config)
    return wizard.run_setup_wizard()


def get_ollama_system_info(config: Config) -> Dict[str, Any]:
    """Convenience function to get system info"""
    wizard = OllamaSetupWizard(config)
    return wizard.get_system_info()