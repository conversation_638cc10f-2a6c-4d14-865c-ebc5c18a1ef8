#!/usr/bin/env python3
"""
Test script for Ollama API improvements
"""

import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ollama_manager():
    """Test the enhanced Ollama manager"""
    print("Testing Enhanced Ollama Manager...")
    print("=" * 50)

    try:
        from utils.config import Config
        from analysis.ollama_manager import OllamaManager, AnalysisRequest

        # Initialize
        config = Config()
        manager = OllamaManager(config)

        # Test 1: Service Status
        print("1. Testing service status...")
        status = manager.get_service_status()
        print(f"   Service status: {status.value}")

        # Test 2: Health Status
        print("\n2. Testing health status...")
        health = manager.get_health_status()
        print(f"   Health status: {health}")

        # Test 3: Connection Test
        print("\n3. Testing connection...")
        connection_test = manager.test_connection()
        print(f"   Connection test: {connection_test}")

        # Test 4: Model Management
        print("\n4. Testing model management...")
        models = manager.get_available_models()
        print(f"   Available models: {len(models)}")
        for model in models[:3]:  # Show first 3
            print(f"     - {model.name}")

        # Test 5: Model Recommendations
        print("\n5. Testing model recommendations...")
        for analysis_type in ["general", "code", "creative"]:
            recommendations = manager.get_model_recommendations(analysis_type)
            print(f"   {analysis_type}: {recommendations}")

        # Test 6: Basic Analysis (if service is running)
        if status.value == "running" and models:
            print("\n6. Testing basic analysis...")
            request = AnalysisRequest(
                prompt="What is artificial intelligence?",
                model=models[0].name,
                max_tokens=50
            )

            print("   Sending analysis request...")
            result = manager.analyze(request)

            if hasattr(result, 'error_type'):  # It's an error
                print(f"   Analysis failed: {result.message}")
                print(f"   Suggestion: {result.suggestion}")
            else:
                response_text = result.get("response", "")[:100]
                print(f"   Analysis successful: {response_text}...")

        print("\n✓ Ollama Manager tests completed")
        return True

    except Exception as e:
        print(f"✗ Ollama Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_client():
    """Test the enhanced Ollama client"""
    print("\nTesting Enhanced Ollama Client...")
    print("=" * 50)

    try:
        from utils.config import Config
        from analysis.ollama_client import OllamaClient

        # Initialize
        config = Config()
        client = OllamaClient(config)

        # Test 1: Availability Check
        print("1. Testing availability...")
        available = client.is_available()
        print(f"   Service available: {available}")

        # Test 2: Model Listing
        print("\n2. Testing model listing...")
        models = client.get_models()
        print(f"   Available models: {len(models)}")
        for model in models[:3]:
            print(f"     - {model}")

        # Test 3: Health Status
        print("\n3. Testing health status...")
        health = client.get_health_status()
        print(f"   Health status: {health}")

        # Test 4: Connection Test
        print("\n4. Testing connection...")
        connection_test = client.test_connection()
        print(f"   Connection test: {connection_test}")

        # Test 5: Model Validation
        if models:
            print("\n5. Testing model validation...")
            model_name = models[0]
            valid = client.validate_model(model_name, auto_pull=False)
            print(f"   Model '{model_name}' valid: {valid}")

        # Test 6: Model Recommendations
        print("\n6. Testing model recommendations...")
        recommendations = client.get_model_recommendations("general")
        print(f"   General analysis recommendations: {recommendations}")

        # Test 7: Basic Generation (if available)
        if available and models:
            print("\n7. Testing basic generation...")
            try:
                result = client.generate(
                    prompt="Hello, how are you?",
                    model=models[0],
                    max_tokens=20
                )

                if result:
                    print(f"   Generation successful: {result[:50]}...")
                else:
                    print("   Generation returned None")

            except Exception as e:
                print(f"   Generation failed: {e}")

        print("\n✓ Enhanced Client tests completed")
        return True

    except Exception as e:
        print(f"✗ Enhanced Client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_setup_wizard():
    """Test the setup wizard"""
    print("\nTesting Setup Wizard...")
    print("=" * 50)

    try:
        from utils.config import Config
        from utils.ollama_setup import OllamaSetupWizard, get_ollama_system_info

        # Initialize
        config = Config()

        # Test 1: System Info
        print("1. Getting system information...")
        system_info = get_ollama_system_info(config)
        print("   System Info:")
        for key, value in system_info.items():
            print(f"     {key}: {value}")

        # Test 2: Setup Wizard Components
        print("\n2. Testing setup wizard components...")
        wizard = OllamaSetupWizard(config)

        # Test individual components (without user interaction)
        print("   - Checking Ollama installation...")
        installed = wizard._check_ollama_installation()
        print(f"     Ollama installed: {installed}")

        print("   - Checking service status...")
        running = wizard._check_service_running()
        print(f"     Service running: {running}")

        if running:
            print("   - Validating connection...")
            connection_valid = wizard._validate_connection()
            print(f"     Connection valid: {connection_valid}")

        print("\n✓ Setup Wizard tests completed")
        return True

    except Exception as e:
        print(f"✗ Setup Wizard test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """Test backward compatibility with existing code"""
    print("\nTesting Backward Compatibility...")
    print("=" * 50)

    try:
        from utils.config import Config
        from analysis.ollama_client import OllamaClient

        # Initialize using old interface
        config = Config()
        client = OllamaClient(config)

        # Test 1: Old method names still work
        print("1. Testing old method compatibility...")

        # is_available() - should work
        available = client.is_available()
        print(f"   is_available(): {available}")

        # get_models() - should work (backward compatibility method)
        models = client.get_models()
        print(f"   get_models(): {len(models)} models")

        # list_models() - new method
        detailed_models = client.list_models()
        print(f"   list_models(): {len(detailed_models)} detailed models")

        # Test 2: Old generate interface
        if available and models:
            print("\n2. Testing old generate interface...")
            try:
                result = client.generate(
                    prompt="Test prompt",
                    model=models[0],
                    temperature=0.7,
                    max_tokens=10,
                    system_prompt="You are a helpful assistant."
                )

                if result:
                    print(f"   Old generate() works: {str(result)[:30]}...")
                else:
                    print("   Old generate() returned None")

            except Exception as e:
                print(f"   Old generate() failed: {e}")

        # Test 3: analyze_topic method
        if available and models:
            print("\n3. Testing analyze_topic method...")
            try:
                result = client.analyze_topic(
                    topic="Python programming",
                    analysis_type="general"
                )

                if result:
                    print(f"   analyze_topic() works: {str(result)[:30]}...")
                else:
                    print("   analyze_topic() returned None")

            except Exception as e:
                print(f"   analyze_topic() failed: {e}")

        print("\n✓ Backward Compatibility tests completed")
        return True

    except Exception as e:
        print(f"✗ Backward Compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Ollama API Improvements Test Suite")
    print("=" * 60)

    tests = [
        ("Enhanced Ollama Manager", test_ollama_manager),
        ("Enhanced Ollama Client", test_enhanced_client),
        ("Setup Wizard", test_setup_wizard),
        ("Backward Compatibility", test_backward_compatibility)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)

    passed = 0
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{status:8} {test_name}")
        if success:
            passed += 1

    print(f"\nResults: {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("\n🎉 All tests passed! Ollama improvements are working correctly.")
        return 0
    else:
        print(f"\n⚠️  {len(results) - passed} tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())