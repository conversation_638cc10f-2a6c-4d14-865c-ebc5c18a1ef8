"""
Enhanced Ollama Manager with robust error handling, model management, and health monitoring
"""

import requests
import json
import time
import threading
import subprocess
import platform
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import hashlib

from utils.logging_setup import LoggerMixin
from utils.config import Config


class OllamaServiceStatus(Enum):
    """Ollama service status enumeration"""
    UNKNOWN = "unknown"
    RUNNING = "running"
    STOPPED = "stopped"
    NOT_INSTALLED = "not_installed"
    ERROR = "error"


class OllamaErrorType(Enum):
    """Types of Ollama errors"""
    CONNECTION_ERROR = "connection_error"
    MODEL_NOT_FOUND = "model_not_found"
    GENERATION_ERROR = "generation_error"
    TIMEOUT_ERROR = "timeout_error"
    SERVICE_ERROR = "service_error"
    VALIDATION_ERROR = "validation_error"


@dataclass
class OllamaError:
    """Structured error information"""
    error_type: OllamaErrorType
    message: str
    suggestion: str
    technical_details: Optional[str] = None
    retry_possible: bool = True


@dataclass
class ModelInfo:
    """Model information structure"""
    name: str
    size: Optional[int] = None
    modified_at: Optional[str] = None
    digest: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    available: bool = True


@dataclass
class AnalysisRequest:
    """Analysis request structure"""
    prompt: str
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    system_prompt: Optional[str] = None
    stream: bool = False
    context: Optional[str] = None
    analysis_type: str = "general"
    request_id: Optional[str] = None


class OllamaManager(LoggerMixin):
    """Enhanced Ollama manager with robust error handling and model management"""

    def __init__(self, config: Config):
        self.config = config
        self.base_url = config.get("ollama.base_url", "http://localhost:11434")
        self.timeout = config.get("ollama.timeout", 30)
        self.default_model = config.get("ollama.default_model", "llama2")

        # Connection management
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})

        # Health monitoring
        self._service_status = OllamaServiceStatus.UNKNOWN
        self._last_health_check = 0
        self._health_check_interval = 30  # seconds
        self._health_check_lock = threading.Lock()

        # Model management
        self._available_models: List[ModelInfo] = []
        self._models_cache_time = 0
        self._models_cache_ttl = 300  # 5 minutes

        # Request caching
        self._request_cache: Dict[str, Any] = {}
        self._cache_ttl = config.get("analysis.cache_ttl_hours", 1) * 3600

        # Error tracking
        self._error_counts: Dict[str, int] = {}
        self._last_errors: List[OllamaError] = []

        # Initialize
        self._initialize()

    def _initialize(self):
        """Initialize the Ollama manager"""
        try:
            self.logger.info("Initializing Ollama Manager")

            # Check service status
            self._update_service_status()

            # Load available models if service is running
            if self._service_status == OllamaServiceStatus.RUNNING:
                self._refresh_models_cache()

        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama Manager: {e}")

    def get_service_status(self) -> OllamaServiceStatus:
        """Get current service status with caching"""
        current_time = time.time()

        with self._health_check_lock:
            if current_time - self._last_health_check > self._health_check_interval:
                self._update_service_status()
                self._last_health_check = current_time

        return self._service_status

    def _update_service_status(self):
        """Update service status by checking Ollama availability"""
        try:
            # Try to connect to Ollama API
            response = self.session.get(
                f"{self.base_url}/api/tags",
                timeout=5
            )

            if response.status_code == 200:
                self._service_status = OllamaServiceStatus.RUNNING
                self.logger.debug("Ollama service is running")
            else:
                self._service_status = OllamaServiceStatus.ERROR
                self.logger.warning(f"Ollama service returned status {response.status_code}")

        except requests.exceptions.ConnectionError:
            # Check if Ollama is installed but not running
            if self._is_ollama_installed():
                self._service_status = OllamaServiceStatus.STOPPED
                self.logger.info("Ollama is installed but not running")
            else:
                self._service_status = OllamaServiceStatus.NOT_INSTALLED
                self.logger.warning("Ollama is not installed")

        except requests.exceptions.Timeout:
            self._service_status = OllamaServiceStatus.ERROR
            self.logger.warning("Ollama service timeout")

        except Exception as e:
            self._service_status = OllamaServiceStatus.ERROR
            self.logger.error(f"Error checking Ollama service: {e}")

    def _is_ollama_installed(self) -> bool:
        """Check if Ollama is installed on the system"""
        try:
            # Try to run ollama --version
            result = subprocess.run(
                ["ollama", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False

    def start_ollama_service(self) -> bool:
        """Attempt to start Ollama service"""
        try:
            self.logger.info("Attempting to start Ollama service")

            if platform.system() == "Windows":
                # On Windows, try to start as a service or background process
                subprocess.Popen(
                    ["ollama", "serve"],
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:
                # On Unix-like systems
                subprocess.Popen(
                    ["ollama", "serve"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )

            # Wait a moment and check if service started
            time.sleep(3)
            self._update_service_status()

            if self._service_status == OllamaServiceStatus.RUNNING:
                self.logger.info("Ollama service started successfully")
                return True
            else:
                self.logger.warning("Failed to start Ollama service")
                return False

        except Exception as e:
            self.logger.error(f"Error starting Ollama service: {e}")
            return False

    def get_available_models(self, force_refresh: bool = False) -> List[ModelInfo]:
        """Get list of available models with caching"""
        current_time = time.time()

        if force_refresh or current_time - self._models_cache_time > self._models_cache_ttl:
            self._refresh_models_cache()

        return self._available_models.copy()

    def _refresh_models_cache(self):
        """Refresh the models cache"""
        try:
            if self.get_service_status() != OllamaServiceStatus.RUNNING:
                self.logger.warning("Cannot refresh models cache - Ollama service not running")
                return

            response = self.session.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()

            data = response.json()
            models = []

            for model_data in data.get("models", []):
                model_info = ModelInfo(
                    name=model_data.get("name", ""),
                    size=model_data.get("size"),
                    modified_at=model_data.get("modified_at"),
                    digest=model_data.get("digest"),
                    details=model_data.get("details", {}),
                    available=True
                )
                models.append(model_info)

            self._available_models = models
            self._models_cache_time = time.time()

            self.logger.info(f"Refreshed models cache: {len(models)} models available")

        except Exception as e:
            self.logger.error(f"Failed to refresh models cache: {e}")

    def is_model_available(self, model_name: str) -> bool:
        """Check if a specific model is available"""
        models = self.get_available_models()
        return any(model.name == model_name for model in models)

    def pull_model(self, model_name: str, progress_callback: Optional[Callable] = None) -> bool:
        """Pull a model from Ollama registry with progress tracking"""
        try:
            self.logger.info(f"Pulling model: {model_name}")

            if self.get_service_status() != OllamaServiceStatus.RUNNING:
                raise Exception("Ollama service is not running")

            # Start model pull
            response = self.session.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                stream=True,
                timeout=300  # 5 minutes timeout for model pulling
            )
            response.raise_for_status()

            # Process streaming response
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))

                        if progress_callback:
                            progress_callback(data)

                        # Check for completion
                        if data.get("status") == "success":
                            self.logger.info(f"Successfully pulled model: {model_name}")
                            self._refresh_models_cache()  # Refresh cache
                            return True

                        # Check for errors
                        if "error" in data:
                            raise Exception(data["error"])

                    except json.JSONDecodeError:
                        continue

            return True

        except Exception as e:
            self.logger.error(f"Failed to pull model {model_name}: {e}")
            return False

    def validate_model(self, model_name: str, auto_pull: bool = True) -> bool:
        """Validate that a model is available, optionally pulling it if missing"""
        try:
            if self.is_model_available(model_name):
                return True

            if auto_pull:
                self.logger.info(f"Model {model_name} not found, attempting to pull...")
                return self.pull_model(model_name)

            return False

        except Exception as e:
            self.logger.error(f"Error validating model {model_name}: {e}")
            return False

    def _create_error(self, error_type: OllamaErrorType, message: str,
                     suggestion: str, technical_details: str = None,
                     retry_possible: bool = True) -> OllamaError:
        """Create a structured error object"""
        error = OllamaError(
            error_type=error_type,
            message=message,
            suggestion=suggestion,
            technical_details=technical_details,
            retry_possible=retry_possible
        )

        # Track error
        self._last_errors.append(error)
        if len(self._last_errors) > 10:  # Keep only last 10 errors
            self._last_errors.pop(0)

        # Count error types
        error_key = error_type.value
        self._error_counts[error_key] = self._error_counts.get(error_key, 0) + 1

        return error

    def _get_cache_key(self, request: AnalysisRequest) -> str:
        """Generate cache key for request"""
        key_data = f"{request.prompt}_{request.model}_{request.temperature}_{request.system_prompt}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _get_cached_response(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached response if available and not expired"""
        if cache_key not in self._request_cache:
            return None

        cached_data = self._request_cache[cache_key]
        if time.time() - cached_data["timestamp"] > self._cache_ttl:
            del self._request_cache[cache_key]
            return None

        return cached_data["response"]

    def _cache_response(self, cache_key: str, response: Dict[str, Any]):
        """Cache a response"""
        self._request_cache[cache_key] = {
            "response": response,
            "timestamp": time.time()
        }

        # Clean old cache entries
        current_time = time.time()
        expired_keys = [
            key for key, data in self._request_cache.items()
            if current_time - data["timestamp"] > self._cache_ttl
        ]
        for key in expired_keys:
            del self._request_cache[key]

    def analyze(self, request: AnalysisRequest, progress_callback: Optional[Callable] = None) -> Union[Dict[str, Any], OllamaError]:
        """Perform analysis with robust error handling and retry logic"""
        try:
            # Validate service status
            if self.get_service_status() != OllamaServiceStatus.RUNNING:
                if not self.start_ollama_service():
                    return self._create_error(
                        OllamaErrorType.SERVICE_ERROR,
                        "Ollama service is not running and could not be started",
                        "Please start Ollama manually or check your installation",
                        retry_possible=False
                    )

            # Use default model if none specified
            model = request.model or self.default_model

            # Validate model availability
            if not self.validate_model(model, auto_pull=True):
                return self._create_error(
                    OllamaErrorType.MODEL_NOT_FOUND,
                    f"Model '{model}' is not available and could not be downloaded",
                    f"Try using a different model or check your internet connection",
                    retry_possible=False
                )

            # Check cache first
            cache_key = self._get_cache_key(request)
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                self.logger.debug(f"Returning cached response for request {request.request_id}")
                return cached_response

            # Prepare request data
            request_data = {
                "model": model,
                "prompt": request.prompt,
                "stream": request.stream
            }

            # Add optional parameters
            if request.system_prompt:
                request_data["system"] = request.system_prompt
            if request.temperature is not None:
                request_data["options"] = request_data.get("options", {})
                request_data["options"]["temperature"] = request.temperature
            if request.max_tokens:
                request_data["options"] = request_data.get("options", {})
                request_data["options"]["num_predict"] = request.max_tokens

            # Perform analysis with retry logic
            max_retries = 3
            retry_delay = 1

            for attempt in range(max_retries):
                try:
                    self.logger.debug(f"Analysis attempt {attempt + 1} for request {request.request_id}")

                    if request.stream:
                        return self._analyze_streaming(request_data, progress_callback)
                    else:
                        return self._analyze_non_streaming(request_data, cache_key)

                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"Timeout on attempt {attempt + 1}, retrying in {retry_delay}s")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                        continue
                    else:
                        return self._create_error(
                            OllamaErrorType.TIMEOUT_ERROR,
                            "Analysis request timed out after multiple attempts",
                            "Try reducing the complexity of your prompt or increasing the timeout setting"
                        )

                except requests.exceptions.ConnectionError:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"Connection error on attempt {attempt + 1}, retrying in {retry_delay}s")
                        # Update service status and try to restart if needed
                        self._update_service_status()
                        if self._service_status != OllamaServiceStatus.RUNNING:
                            self.start_ollama_service()
                        time.sleep(retry_delay)
                        retry_delay *= 2
                        continue
                    else:
                        return self._create_error(
                            OllamaErrorType.CONNECTION_ERROR,
                            "Could not connect to Ollama service after multiple attempts",
                            "Check if Ollama is running and accessible"
                        )

                except Exception as e:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"Error on attempt {attempt + 1}: {e}, retrying in {retry_delay}s")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                        continue
                    else:
                        return self._create_error(
                            OllamaErrorType.GENERATION_ERROR,
                            f"Analysis failed after multiple attempts: {str(e)}",
                            "Try simplifying your prompt or using a different model",
                            technical_details=str(e)
                        )

        except Exception as e:
            self.logger.error(f"Unexpected error in analyze: {e}")
            return self._create_error(
                OllamaErrorType.GENERATION_ERROR,
                f"Unexpected error during analysis: {str(e)}",
                "Please try again or contact support if the problem persists",
                technical_details=str(e)
            )

    def _analyze_non_streaming(self, request_data: Dict[str, Any], cache_key: str) -> Dict[str, Any]:
        """Perform non-streaming analysis"""
        response = self.session.post(
            f"{self.base_url}/api/generate",
            json=request_data,
            timeout=self.timeout
        )
        response.raise_for_status()

        result = response.json()

        # Validate response
        if not result.get("response"):
            raise Exception("Empty response from Ollama")

        # Cache the response
        self._cache_response(cache_key, result)

        return result

    def _analyze_streaming(self, request_data: Dict[str, Any], progress_callback: Optional[Callable]) -> Dict[str, Any]:
        """Perform streaming analysis"""
        response = self.session.post(
            f"{self.base_url}/api/generate",
            json=request_data,
            stream=True,
            timeout=self.timeout
        )
        response.raise_for_status()

        full_response = ""
        response_data = {}

        for line in response.iter_lines():
            if line:
                try:
                    data = json.loads(line.decode('utf-8'))

                    if "response" in data:
                        full_response += data["response"]

                        if progress_callback:
                            progress_callback({
                                "partial_response": data["response"],
                                "full_response": full_response,
                                "done": data.get("done", False)
                            })

                    if data.get("done"):
                        response_data = data
                        response_data["response"] = full_response
                        break

                except json.JSONDecodeError:
                    continue

        if not response_data:
            raise Exception("No valid response received from streaming")

        return response_data

    def get_model_recommendations(self, analysis_type: str) -> List[str]:
        """Get recommended models for specific analysis types"""
        recommendations = {
            "general": ["llama2", "mistral", "codellama"],
            "code": ["codellama", "deepseek-coder", "starcoder"],
            "creative": ["llama2", "mistral", "vicuna"],
            "technical": ["codellama", "llama2", "mistral"],
            "research": ["llama2", "mistral", "openchat"]
        }

        return recommendations.get(analysis_type, recommendations["general"])

    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        status = {
            "service_status": self.get_service_status().value,
            "available_models": len(self._available_models),
            "cache_size": len(self._request_cache),
            "error_counts": self._error_counts.copy(),
            "last_health_check": self._last_health_check,
            "base_url": self.base_url
        }

        # Add recent errors
        if self._last_errors:
            status["recent_errors"] = [
                {
                    "type": error.error_type.value,
                    "message": error.message,
                    "suggestion": error.suggestion
                }
                for error in self._last_errors[-3:]  # Last 3 errors
            ]

        return status

    def clear_cache(self):
        """Clear the request cache"""
        self._request_cache.clear()
        self.logger.info("Request cache cleared")

    def reset_error_counts(self):
        """Reset error counters"""
        self._error_counts.clear()
        self._last_errors.clear()
        self.logger.info("Error counts reset")

    def test_connection(self) -> Dict[str, Any]:
        """Test connection to Ollama service"""
        test_result = {
            "success": False,
            "service_status": self.get_service_status().value,
            "response_time": None,
            "available_models": 0,
            "error": None
        }

        try:
            start_time = time.time()

            # Test basic connectivity
            response = self.session.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()

            response_time = time.time() - start_time
            test_result["response_time"] = round(response_time * 1000, 2)  # ms

            # Count available models
            data = response.json()
            test_result["available_models"] = len(data.get("models", []))

            test_result["success"] = True

        except Exception as e:
            test_result["error"] = str(e)

        return test_result

    def cleanup(self):
        """Cleanup resources"""
        try:
            self.session.close()
            self._request_cache.clear()
            self.logger.info("OllamaManager cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


# Convenience functions for backward compatibility
def create_ollama_manager(config: Config) -> OllamaManager:
    """Create and return an OllamaManager instance"""
    return OllamaManager(config)


def test_ollama_connection(config: Config) -> Dict[str, Any]:
    """Test Ollama connection and return status"""
    manager = OllamaManager(config)
    return manager.test_connection()


def get_available_models(config: Config) -> List[str]:
    """Get list of available model names"""
    manager = OllamaManager(config)
    models = manager.get_available_models()
    return [model.name for model in models]