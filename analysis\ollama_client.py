"""
Enhanced Ollama API client with robust error handling and improved reliability
"""

import requests
import json
import time
from typing import Dict, Any, List, Optional, Generator, Callable, Union
from utils.logging_setup import LoggerMixin
from utils.config import Config

# Import the new manager if available, fallback to basic implementation
try:
    from .ollama_manager import OllamaManager, AnalysisRequest, OllamaError, OllamaErrorType, OllamaServiceStatus
    ENHANCED_MODE = True
except ImportError:
    ENHANCED_MODE = False

class OllamaClient(LoggerMixin):
    """Enhanced client for interacting with Ollama API with robust error handling"""

    def __init__(self, config: Config):
        self.config = config
        self.base_url = config.get("ollama.base_url", "http://localhost:11434")
        self.timeout = config.get("ollama.timeout", 30)
        self.default_model = config.get("ollama.default_model", "llama2")

        if ENHANCED_MODE:
            # Use enhanced manager
            self.manager = OllamaManager(config)
            self.logger.info("Using enhanced Ollama manager")
        else:
            # Fallback to basic session
            self.session = requests.Session()
            self.manager = None
            self.logger.info("Using basic Ollama client")

    def is_available(self) -> bool:
        """Check if Ollama service is available"""
        if ENHANCED_MODE and self.manager:
            try:
                status = self.manager.get_service_status()
                return status == OllamaServiceStatus.RUNNING
            except Exception as e:
                self.logger.error(f"Enhanced availability check failed: {e}")
                return False
        else:
            # Fallback to basic check
            try:
                response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
                return response.status_code == 200
            except Exception as e:
                self.logger.error(f"Ollama service not available: {e}")
                return False

    def list_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        if ENHANCED_MODE and self.manager:
            try:
                models = self.manager.get_available_models()
                return [
                    {
                        "name": model.name,
                        "size": model.size,
                        "modified_at": model.modified_at,
                        "digest": model.digest,
                        "details": model.details or {}
                    }
                    for model in models
                ]
            except Exception as e:
                self.logger.error(f"Enhanced model listing failed: {e}")
                return []
        else:
            # Fallback to basic implementation
            try:
                response = self.session.get(f"{self.base_url}/api/tags", timeout=self.timeout)
                response.raise_for_status()
                data = response.json()
                return data.get("models", [])
            except Exception as e:
                self.logger.error(f"Failed to list models: {e}")
                return []

    def get_models(self) -> List[str]:
        """Get list of available model names (backward compatibility)"""
        models = self.list_models()
        return [model.get("name", "") for model in models if model.get("name")]

    def pull_model(self, model_name: str, progress_callback: Optional[Callable] = None) -> bool:
        """Pull a model from Ollama registry with optional progress tracking"""
        if ENHANCED_MODE and self.manager:
            try:
                return self.manager.pull_model(model_name, progress_callback)
            except Exception as e:
                self.logger.error(f"Enhanced model pull failed for {model_name}: {e}")
                return False
        else:
            # Fallback to basic implementation
            try:
                self.logger.info(f"Pulling model: {model_name}")
                response = self.session.post(
                    f"{self.base_url}/api/pull",
                    json={"name": model_name},
                    timeout=300  # 5 minutes for model download
                )
                response.raise_for_status()
                return True
            except Exception as e:
                self.logger.error(f"Failed to pull model {model_name}: {e}")
                return False
    
    def generate(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[str, Dict[str, Any], None]:
        """
        Generate text using Ollama with enhanced error handling

        Args:
            prompt: Input prompt
            model: Model name (uses default if None)
            temperature: Generation temperature
            max_tokens: Maximum tokens to generate
            system_prompt: System prompt for context
            stream: Whether to stream response
            **kwargs: Additional parameters

        Returns:
            Generated text, full response dict, or None if failed
        """
        if ENHANCED_MODE and self.manager:
            try:
                # Use enhanced manager
                request = AnalysisRequest(
                    prompt=prompt,
                    model=model or self.default_model,
                    temperature=temperature or self.config.get("ollama.temperature", 0.7),
                    max_tokens=max_tokens or self.config.get("ollama.max_tokens", 2048),
                    system_prompt=system_prompt,
                    stream=stream,
                    analysis_type=kwargs.get('analysis_type', 'general'),
                    request_id=kwargs.get('request_id')
                )

                result = self.manager.analyze(request, kwargs.get('progress_callback'))

                # Handle errors
                if isinstance(result, OllamaError):
                    self.logger.error(f"Generation failed: {result.message}. {result.suggestion}")
                    return None

                # Return based on what caller expects
                if kwargs.get('return_full_response', False):
                    return result
                else:
                    return result.get("response", "")

            except Exception as e:
                self.logger.error(f"Enhanced generation failed: {e}")
                return None
        else:
            # Fallback to basic implementation
            return self._generate_basic(prompt, model, temperature, max_tokens, system_prompt, stream)

    def _generate_basic(self, prompt: str, model: Optional[str], temperature: Optional[float],
                       max_tokens: Optional[int], system_prompt: Optional[str], stream: bool) -> Optional[str]:
        """Basic generation implementation for fallback"""
        try:
            model = model or self.default_model
            temperature = temperature or self.config.get("ollama.temperature", 0.7)
            max_tokens = max_tokens or self.config.get("ollama.max_tokens", 2048)

            payload = {
                "model": model,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }

            if system_prompt:
                payload["system"] = system_prompt

            self.logger.debug(f"Generating with model {model}, prompt length: {len(prompt)}")

            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()

            if stream:
                return self._handle_stream_response(response)
            else:
                data = response.json()
                return data.get("response", "")

        except Exception as e:
            self.logger.error(f"Basic generation failed: {e}")
            return None

    # Enhanced methods available only when manager is loaded
    def generate_streaming(self, prompt: str, model: Optional[str] = None,
                          system_prompt: Optional[str] = None,
                          progress_callback: Optional[Callable] = None,
                          **kwargs) -> Union[Dict[str, Any], None]:
        """Generate streaming response with progress tracking"""
        if ENHANCED_MODE and self.manager:
            try:
                request = AnalysisRequest(
                    prompt=prompt,
                    model=model or self.default_model,
                    system_prompt=system_prompt,
                    temperature=kwargs.get('temperature'),
                    max_tokens=kwargs.get('max_tokens'),
                    stream=True,
                    analysis_type=kwargs.get('analysis_type', 'general')
                )

                result = self.manager.analyze(request, progress_callback)

                if isinstance(result, OllamaError):
                    self.logger.error(f"Streaming generation failed: {result.message}")
                    return None

                return result

            except Exception as e:
                self.logger.error(f"Enhanced streaming generation failed: {e}")
                return None
        else:
            self.logger.warning("Streaming generation requires enhanced mode")
            return None

    def validate_model(self, model_name: str, auto_pull: bool = True) -> bool:
        """Validate that a model is available"""
        if ENHANCED_MODE and self.manager:
            try:
                return self.manager.validate_model(model_name, auto_pull)
            except Exception as e:
                self.logger.error(f"Model validation failed for {model_name}: {e}")
                return False
        else:
            # Basic validation - check if model is in list
            models = self.get_models()
            return model_name in models

    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        if ENHANCED_MODE and self.manager:
            return self.manager.get_health_status()
        else:
            # Basic health check
            return {
                "service_status": "running" if self.is_available() else "stopped",
                "available_models": len(self.get_models()),
                "base_url": self.base_url,
                "enhanced_mode": False
            }

    def test_connection(self) -> Dict[str, Any]:
        """Test connection to Ollama service"""
        if ENHANCED_MODE and self.manager:
            return self.manager.test_connection()
        else:
            # Basic connection test
            try:
                start_time = time.time()
                available = self.is_available()
                response_time = (time.time() - start_time) * 1000

                return {
                    "success": available,
                    "response_time": round(response_time, 2),
                    "available_models": len(self.get_models()) if available else 0,
                    "enhanced_mode": False
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "enhanced_mode": False
                }

    def start_service(self) -> bool:
        """Attempt to start Ollama service"""
        if ENHANCED_MODE and self.manager:
            try:
                return self.manager.start_ollama_service()
            except Exception as e:
                self.logger.error(f"Failed to start Ollama service: {e}")
                return False
        else:
            self.logger.warning("Service start requires enhanced mode")
            return False

    def get_model_recommendations(self, analysis_type: str = "general") -> List[str]:
        """Get recommended models for analysis type"""
        if ENHANCED_MODE and self.manager:
            return self.manager.get_model_recommendations(analysis_type)
        else:
            # Basic recommendations
            recommendations = {
                "general": ["llama2", "mistral"],
                "code": ["codellama"],
                "creative": ["llama2"],
                "technical": ["llama2", "codellama"],
                "research": ["llama2", "mistral"]
            }
            return recommendations.get(analysis_type, recommendations["general"])

    def clear_cache(self):
        """Clear the request cache"""
        if ENHANCED_MODE and self.manager:
            self.manager.clear_cache()
        else:
            self.logger.info("No cache to clear in basic mode")

    def cleanup(self):
        """Cleanup resources"""
        if ENHANCED_MODE and self.manager:
            self.manager.cleanup()
        elif hasattr(self, 'session'):
            self.session.close()

    def generate_stream(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None
    ) -> Generator[str, None, None]:
        """
        Generate text with streaming response (backward compatibility)

        Args:
            prompt: Input prompt
            model: Model name
            temperature: Generation temperature
            max_tokens: Maximum tokens
            system_prompt: System prompt

        Yields:
            Text chunks as they are generated
        """
        if ENHANCED_MODE and self.manager:
            # Use enhanced streaming
            def progress_callback(data):
                if "partial_response" in data:
                    return data["partial_response"]

            try:
                result = self.generate_streaming(
                    prompt=prompt,
                    model=model,
                    system_prompt=system_prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    progress_callback=lambda data: None  # We'll handle streaming differently
                )

                if result and "response" in result:
                    # For backward compatibility, yield the full response
                    yield result["response"]

            except Exception as e:
                self.logger.error(f"Enhanced streaming failed: {e}")
                return
        else:
            # Fallback to basic streaming
            try:
                model = model or self.default_model
                temperature = temperature or self.config.get("ollama.temperature", 0.7)
                max_tokens = max_tokens or self.config.get("ollama.max_tokens", 2048)

                payload = {
                    "model": model,
                    "prompt": prompt,
                    "stream": True,
                    "options": {
                        "temperature": temperature,
                        "num_predict": max_tokens
                    }
                }

                if system_prompt:
                    payload["system"] = system_prompt

                response = self.session.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    stream=True,
                    timeout=self.timeout
                )
                response.raise_for_status()

                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line.decode('utf-8'))
                            if 'response' in data:
                                yield data['response']
                            if data.get('done', False):
                                break
                        except json.JSONDecodeError:
                            continue

            except Exception as e:
                self.logger.error(f"Basic streaming generation failed: {e}")
                yield ""
    
    def _handle_stream_response(self, response: requests.Response) -> str:
        """Handle streaming response and return complete text"""
        complete_text = ""
        try:
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if 'response' in data:
                            complete_text += data['response']
                        if data.get('done', False):
                            break
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            self.logger.error(f"Error handling stream response: {e}")
        
        return complete_text
    
    def analyze_topic(
        self,
        topic: str,
        context: Optional[str] = None,
        analysis_type: str = "general"
    ) -> Optional[str]:
        """
        Analyze a topic using AI with enhanced error handling
        
        Args:
            topic: Topic to analyze
            context: Additional context
            analysis_type: Type of analysis (general, detailed, connections)
            
        Returns:
            Analysis result or None if failed
        """
        prompts = {
            "general": f"Analyze the following topic and provide insights: {topic}",
            "detailed": f"Provide a detailed analysis of: {topic}. Include key aspects, implications, and related concepts.",
            "connections": f"Analyze the topic '{topic}' and identify potential connections to other concepts, ideas, or domains."
        }
        
        system_prompts = {
            "general": "You are an expert analyst. Provide clear, structured insights.",
            "detailed": "You are a research analyst. Provide comprehensive, well-structured analysis.",
            "connections": "You are a systems thinker. Focus on identifying relationships and connections."
        }
        
        prompt = prompts.get(analysis_type, prompts["general"])
        system_prompt = system_prompts.get(analysis_type, system_prompts["general"])
        
        if context:
            prompt += f"\n\nAdditional context: {context}"

        # Use enhanced generation with analysis type
        return self.generate(
            prompt=prompt,
            system_prompt=system_prompt,
            analysis_type=analysis_type
        )
    
    def find_connections(self, topics: List[str]) -> Optional[str]:
        """
        Find connections between multiple topics
        
        Args:
            topics: List of topics to analyze
            
        Returns:
            Connection analysis or None if failed
        """
        topics_str = ", ".join(topics)
        prompt = f"Analyze the connections and relationships between these topics: {topics_str}. Identify common themes, contrasts, and potential synergies."
        
        system_prompt = "You are an expert in systems thinking and pattern recognition. Focus on identifying meaningful connections and relationships."
        
        return self.generate(
            prompt=prompt,
            system_prompt=system_prompt,
            analysis_type="connections"
        )
    
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific model"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/show",
                json={"name": model_name},
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Failed to get model info for {model_name}: {e}")
            return None
