"""
Main window for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from typing import Dict, Any

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import show_error, show_info, confirm_action
from utils.theme import <PERSON><PERSON><PERSON>ger
from typing import Dict, Any
from analysis.ollama_client import OllamaClient
from analysis.enhanced_ollama_client import EnhancedOllamaClient
from analysis.advanced_analyzers import analysis_registry
from gui.topic_input import TopicInputPanel
from gui.topic_list import TopicListPanel
from gui.analysis_panel import AnalysisPanel
from gui.results_viewer import ResultsViewer
from gui.settings_dialog import SettingsDialog
from gui.progress_dialog import AnalysisProgressManager
from data.project_manager import ProjectManager
from gui.widgets import ScrollableFrame

class MainWindow(LoggerMixin):
    """Main application window"""
    
    def __init__(self, root: tk.Tk, config: Config):
        self.root = root
        self.config = config
        self.ollama_client = OllamaClient(config)
        self.enhanced_client = EnhancedOllamaClient(config)
        self.project_manager = ProjectManager(config)
        self.progress_manager = AnalysisProgressManager(root)

        # Initialize theme manager
        self.theme = ThemeManager(root)

        # Initialize components
        self.topic_input = None
        self.topic_list = None
        self.analysis_panel = None
        self.results_viewer = None

        # State variables
        self.current_analysis = None
        self.analysis_running = False
        self.current_project_id = None

        self.setup_ui()
        self.setup_menu()
        self.check_ollama_connection()

    def _prepare_analysis_params(self, topic_data: Dict[str, Any], analysis_config: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare parameters for ETA prediction"""
        # Count topics/sub-topics
        topic_count = 1  # Main topic

        if "sub_topics_tree" in topic_data:
            topic_count += self._count_topics_in_tree(topic_data["sub_topics_tree"])
        elif "sub_topics" in topic_data:
            topic_count += len(topic_data["sub_topics"])

        return {
            "type": analysis_config.get("type", "iterative"),
            "model": analysis_config.get("model", "llama2"),
            "topic_count": topic_count,
            "temperature": analysis_config.get("temperature", 0.7),
            "max_depth": analysis_config.get("max_depth", 3),
            "parallel_processing": analysis_config.get("parallel_processing", False),
            "enable_caching": analysis_config.get("enable_caching", True),
            "main_topic": topic_data.get("title", "Unknown Topic")
        }

    def _count_topics_in_tree(self, tree: list) -> int:
        """Recursively count topics in a tree structure"""
        count = 0
        for item in tree:
            count += 1
            if isinstance(item, dict) and "children" in item:
                count += self._count_topics_in_tree(item["children"])
            elif isinstance(item, dict) and "sub_topics" in item:
                count += self._count_topics_in_tree(item["sub_topics"])
        return count

    def _get_analysis_steps(self, analysis_type: str) -> list:
        """Get appropriate steps for different analysis types"""
        step_configs = {
            "iterative": [
                "Initializing analysis framework",
                "Analyzing sub-topics sequentially",
                "Discovering topic connections",
                "Generating synthesis"
            ],
            "recursive": [
                "Initializing recursive framework",
                "Analyzing main topic",
                "Generating and analyzing sub-topics",
                "Building topic hierarchy",
                "Creating comprehensive synthesis"
            ],
            "comparative": [
                "Setting up comparison framework",
                "Analyzing individual topics",
                "Performing comparative analysis",
                "Scoring and ranking",
                "Generating recommendations"
            ],
            "swot": [
                "Initializing SWOT framework",
                "Identifying strengths",
                "Identifying weaknesses",
                "Identifying opportunities",
                "Identifying threats",
                "Creating strategic synthesis"
            ],
            "temporal": [
                "Setting up temporal framework",
                "Analyzing historical context",
                "Identifying current trends",
                "Generating future predictions",
                "Creating timeline synthesis"
            ]
        }

        return step_configs.get(analysis_type, step_configs["iterative"])

    def _run_iterative_with_progress(self, topic_data, analysis_config, progress_callback, is_cancelled):
        """Run iterative analysis with detailed progress reporting"""
        try:
            # Check if enhanced analysis is enabled
            use_enhanced = analysis_config.get("use_enhanced_analysis", True)

            if use_enhanced:
                return self._run_enhanced_with_progress(topic_data, analysis_config, progress_callback, is_cancelled)
            else:
                return self._run_legacy_with_progress(topic_data, analysis_config, progress_callback, is_cancelled)

        except Exception as e:
            self.logger.error(f"Iterative analysis failed: {e}")
            return {"error": str(e)}

    def _run_enhanced_with_progress(self, topic_data, analysis_config, progress_callback, is_cancelled):
        """Run enhanced analysis with progress tracking"""
        main_topic = topic_data.get("title", "")
        sub_topics_tree = topic_data.get("sub_topics_tree", [])

        # Create a custom progress callback for the enhanced client
        def enhanced_progress_callback(phase, current, total, details=""):
            if is_cancelled():
                return

            # Map enhanced client phases to our progress steps
            phase_mapping = {
                "initialization": 1.0,
                "topic_analysis": 1.5,
                "connections": 2.0,
                "synthesis": 2.5
            }

            base_step = phase_mapping.get(phase, 1.0)
            if total > 0:
                phase_progress = (current / total) * 0.5  # Each phase is 0.5 steps
                step = base_step + phase_progress
            else:
                step = base_step

            progress_callback(step, f"{phase.title()}: {details}",
                            f"Processing {current}/{total}" if total > 0 else details)

        # Use enhanced client with progress tracking
        results = self.enhanced_client.progressive_analysis_with_progress(
            main_topic=main_topic,
            sub_topics_tree=sub_topics_tree,
            config=analysis_config,
            progress_callback=enhanced_progress_callback
        )

        return results

    def _run_recursive_with_progress(self, topic_data, analysis_config, progress_callback, is_cancelled):
        """Run recursive analysis with progress tracking"""
        try:
            main_topic = topic_data.get("title", "")
            max_depth = analysis_config.get("max_depth", 3)

            progress_callback(1, "Starting recursive analysis...",
                            f"Main topic: {main_topic}")

            results = self.run_recursive_analysis(topic_data, analysis_config)

            # Update progress as we go through recursive levels
            for depth in range(1, max_depth + 1):
                if is_cancelled():
                    return {"error": "Analysis cancelled by user"}

                step = 1 + (depth / max_depth) * 2  # Steps 1-3 for recursive phases
                progress_callback(step, f"Analyzing depth level {depth}...",
                                f"Exploring sub-topics at depth {depth}")

            return results

        except Exception as e:
            self.logger.error(f"Recursive analysis failed: {e}")
            return {"error": str(e)}

    def _run_advanced_with_progress(self, topic_data, analysis_config, progress_callback, is_cancelled):
        """Run advanced analysis (SWOT, Comparative, Temporal) with progress tracking"""
        try:
            analysis_type = analysis_config.get("type", "comparative")

            # Map analysis phases to progress steps
            if analysis_type == "swot":
                phases = ["strengths", "weaknesses", "opportunities", "threats", "synthesis"]
            elif analysis_type == "comparative":
                phases = ["individual_analysis", "comparison", "scoring", "recommendations"]
            elif analysis_type == "temporal":
                phases = ["historical", "current_trends", "predictions", "timeline"]
            else:
                phases = ["analysis", "processing", "synthesis"]

            results = self.run_advanced_analysis(topic_data, analysis_config)

            # Simulate progress through phases
            for i, phase in enumerate(phases):
                if is_cancelled():
                    return {"error": "Analysis cancelled by user"}

                step = 1 + (i / len(phases)) * 2  # Distribute across steps 1-3
                progress_callback(step, f"Processing {phase.replace('_', ' ')}...",
                                f"Phase {i+1}/{len(phases)}: {phase}")

            return results

        except Exception as e:
            self.logger.error(f"Advanced analysis failed: {e}")
            return {"error": str(e)}

    def _run_legacy_with_progress(self, topic_data, analysis_config, progress_callback, is_cancelled):
        """Run legacy analysis with progress tracking"""
        try:
            sub_topics = topic_data.get("sub_topics", [])

            progress_callback(1, "Running legacy analysis...",
                            f"Processing {len(sub_topics)} sub-topics")

            results = self.run_legacy_analysis(topic_data, analysis_config)

            return results

        except Exception as e:
            self.logger.error(f"Legacy analysis failed: {e}")
            return {"error": str(e)}
    
    def setup_ui(self):
        """Set up the main user interface"""
        # Create main container with themed styling
        main_frame = ttk.Frame(self.root, style="Main.TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add toolbar
        self.setup_toolbar(main_frame)

        # Create paned window for resizable panels
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # Left panel - Topic List and Topic Input with scrollable frame
        left_container = ScrollableFrame(paned_window)
        left_main_frame = ttk.Frame(left_container.get_frame())
        left_main_frame.pack(fill=tk.BOTH, expand=True)

        # Create vertical paned window for topic list and topic input
        left_paned = ttk.PanedWindow(left_main_frame, orient=tk.VERTICAL)
        left_paned.pack(fill=tk.BOTH, expand=True)

        # Topic List panel (top)
        topic_list_frame = ttk.LabelFrame(
            left_paned,
            text="📚 Topic Library",
            padding=5
        )
        self.topic_list = TopicListPanel(topic_list_frame, self.config, self.theme, self.on_topic_selected)
        left_paned.add(topic_list_frame, weight=1)

        # Topic Input panel (bottom)
        topic_input_frame = ttk.LabelFrame(
            left_paned,
            text="📝 Topic Input",
            padding=5
        )
        self.topic_input = TopicInputPanel(topic_input_frame, self.config, self.theme)
        left_paned.add(topic_input_frame, weight=1)

        paned_window.add(left_container, weight=1)

        # Right panel container
        right_container = ttk.Frame(paned_window, style="Main.TFrame")
        right_container.pack_propagate(False)
        paned_window.add(right_container, weight=2)


        # Right panel - Analysis Control (top) and Results (bottom) in a vertical PanedWindow
        right_paned = ttk.PanedWindow(right_container, orient=tk.VERTICAL)
        right_paned.pack(fill=tk.BOTH, expand=True)

        # Analysis Control panel (top) with scrollable frame
        analysis_scrollable = ScrollableFrame(right_paned)
        analysis_frame = ttk.LabelFrame(
            analysis_scrollable.get_frame(),
            text="⚙️ Analysis Control",
            padding=10
        )
        analysis_frame.pack(fill=tk.BOTH, expand=True)
        self.analysis_panel = AnalysisPanel(analysis_frame, self.config, self.on_start_analysis, self.theme)
        right_paned.add(analysis_scrollable, weight=1)

        # Results panel (bottom) with scrollable frame
        results_scrollable = ScrollableFrame(right_paned)
        results_frame = ttk.LabelFrame(
            results_scrollable.get_frame(),
            text="📊 Results",
            padding=5
        )
        results_frame.pack(fill=tk.BOTH, expand=True)
        self.results_viewer = ResultsViewer(results_frame, self.config, self.theme)
        right_paned.add(results_scrollable, weight=3)

        # Status bar
        self.setup_status_bar()

    def on_topic_selected(self, topic_data: Dict[str, Any]):
        """Handle topic selection from the topic library"""
        try:
            # Load the selected topic into the topic input panel
            self.topic_input.set_topic_data(topic_data)

            # Update status
            self.update_status(f"Loaded topic: {topic_data.get('title', 'Unknown')}")

            # Show success message
            show_info("Topic Loaded", f"Topic '{topic_data.get('title', 'Unknown')}' loaded successfully.")

        except Exception as e:
            self.logger.error(f"Failed to load selected topic: {e}")
            show_error("Load Error", f"Failed to load selected topic: {str(e)}")

    def create_scrollable_frame(self, parent):
        """Create a scrollable frame for better content management"""
        # Create canvas and scrollbar
        canvas = tk.Canvas(parent, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollbar and canvas
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind("<MouseWheel>", _on_mousewheel)
        
        return scrollable_frame
        self.setup_status_bar()

    def setup_toolbar(self, parent):
        """Set up the application toolbar"""
        toolbar_frame = ttk.Frame(parent, style="Panel.TFrame")
        toolbar_frame.pack(fill=tk.X, pady=(0, self.theme.spacing.pad_md))

        # Quick action buttons
        ttk.Button(
            toolbar_frame,
            text="🆕 New",
            command=self.new_analysis,
            style="TButton"
        ).pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm))

        ttk.Button(
            toolbar_frame,
            text="📂 Open",
            command=self.open_analysis,
            style="TButton"
        ).pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm))

        ttk.Button(
            toolbar_frame,
            text="💾 Save",
            command=self.save_analysis,
            style="TButton"
        ).pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm))

        # Separator
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=self.theme.spacing.pad_md
        )

        ttk.Button(
            toolbar_frame,
            text="▶️ Start Analysis",
            command=self.start_analysis,
            style="Primary.TButton"
        ).pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm))

        ttk.Button(
            toolbar_frame,
            text="⏹️ Stop",
            command=self.stop_analysis,
            style="Error.TButton"
        ).pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm))

        # Right side - Settings and help
        ttk.Button(
            toolbar_frame,
            text="⚙️ Settings",
            command=self.show_settings,
            style="TButton"
        ).pack(side=tk.RIGHT, padx=(self.theme.spacing.pad_sm, 0))

        ttk.Button(
            toolbar_frame,
            text="❓ Help",
            command=self.show_user_guide,
            style="TButton"
        ).pack(side=tk.RIGHT, padx=(self.theme.spacing.pad_sm, 0))
    
    def setup_status_bar(self):
        """Set up the status bar with enhanced styling"""
        self.status_frame = ttk.Frame(self.root, style="Panel.TFrame")
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=(0, 5))

        # Add a subtle separator line
        separator = ttk.Separator(self.status_frame, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, pady=(0, 3))

        # Status content frame
        status_content = ttk.Frame(self.status_frame, style="Panel.TFrame")
        status_content.pack(fill=tk.X, padx=5, pady=3)

        # Left side - Status information
        left_status = ttk.Frame(status_content, style="Panel.TFrame")
        left_status.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Status label with icon
        status_icon_frame = ttk.Frame(left_status, style="Panel.TFrame")
        status_icon_frame.pack(side=tk.LEFT, anchor=tk.W)

        ttk.Label(status_icon_frame, text="ℹ️", style="TLabel").pack(side=tk.LEFT)

        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(
            status_icon_frame,
            textvariable=self.status_var,
            style="TLabel"
        )
        self.status_label.pack(side=tk.LEFT, padx=(3, 0))

        # Right side - Progress and controls
        right_status = ttk.Frame(status_content, style="Panel.TFrame")
        right_status.pack(side=tk.RIGHT, anchor=tk.E)

        # Progress bar with enhanced styling
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            right_status,
            variable=self.progress_var,
            mode='determinate',
            length=150,
            style="TProgressbar"
        )
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))

        # Progress label
        self.progress_text_var = tk.StringVar(value="")
        self.progress_text_label = ttk.Label(
            right_status,
            textvariable=self.progress_text_var,
            style="TLabel"
        )
        self.progress_text_label.pack(side=tk.RIGHT, padx=(0, 5))

        # Connection status with enhanced styling
        connection_frame = ttk.Frame(status_content, style="Panel.TFrame")
        connection_frame.pack(side=tk.RIGHT, padx=(5, 10))

        ttk.Label(connection_frame, text="🔗", style="TLabel").pack(side=tk.LEFT)

        self.connection_var = tk.StringVar(value="Checking...")
        self.connection_label = ttk.Label(
            connection_frame,
            textvariable=self.connection_var,
            style="TLabel"
        )
        self.connection_label.pack(side=tk.LEFT, padx=(self.theme.spacing.pad_sm, 0))
    
    def setup_menu(self):
        """Set up the menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Analysis", command=self.new_analysis)
        file_menu.add_command(label="Open Analysis", command=self.open_analysis)
        file_menu.add_command(label="Save Analysis", command=self.save_analysis)
        file_menu.add_separator()
        file_menu.add_command(label="Export Results", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Clear All", command=self.clear_all)
        edit_menu.add_command(label="Settings", command=self.show_settings)
        
        # Analysis menu
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Analysis", menu=analysis_menu)
        analysis_menu.add_command(label="Start Analysis", command=self.start_analysis)
        analysis_menu.add_command(label="Stop Analysis", command=self.stop_analysis)
        analysis_menu.add_separator()
        analysis_menu.add_command(label="View Connections", command=self.view_connections)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="User Guide", command=self.show_user_guide)
    
    def check_ollama_connection(self):
        """Check Ollama connection in background"""
        def check_connection():
            try:
                if self.ollama_client.is_available():
                    self.root.after(0, lambda: self.update_connection_status("Connected", "green"))
                    models = self.ollama_client.list_models()
                    self.root.after(0, lambda: self.analysis_panel.update_models(models))
                else:
                    self.root.after(0, lambda: self.update_connection_status("Disconnected", "red"))
            except Exception as e:
                self.logger.error(f"Connection check failed: {e}")
                self.root.after(0, lambda: self.update_connection_status("Error", "red"))
        
        threading.Thread(target=check_connection, daemon=True).start()
    
    def update_connection_status(self, status: str, color: str):
        """Update connection status display"""
        # Map colors to theme-appropriate styles
        style_map = {
            "green": "Success.TLabel",
            "red": "Error.TLabel",
            "orange": "Warning.TLabel"
        }

        self.connection_var.set(f"Ollama: {status}")
        style = style_map.get(color, "Muted.TLabel")
        self.connection_label.configure(style=style)
    
    def update_status(self, message: str):
        """Update status bar message"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def update_progress(self, value: float):
        """Update progress bar"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def on_start_analysis(self, analysis_config: Dict[str, Any]):
        """Handle analysis start request with enhanced ETA prediction"""
        if self.analysis_running:
            show_error("Analysis Running", "An analysis is already in progress.")
            return

        # Get topic data
        topic_data = self.topic_input.get_topic_data()
        if not topic_data:
            show_error("No Topic", "Please enter a topic to analyze.")
            return

        # Prepare analysis parameters for ETA prediction
        analysis_params = self._prepare_analysis_params(topic_data, analysis_config)

        # Determine steps based on analysis type
        steps = self._get_analysis_steps(analysis_config.get("type", "iterative"))

        # Start analysis in background thread with enhanced ETA/progress dialog
        self.progress_manager.start_analysis(
            lambda progress_callback, is_cancelled: self._run_analysis_with_progress(topic_data, analysis_config, progress_callback, is_cancelled),
            steps=steps,
            title=f"{analysis_config.get('type', 'Iterative').title()} Analysis Progress",
            analysis_params=analysis_params
        )

    def _flatten_subtopics(self, subtopics_tree):
        """Flatten nested sub-topic tree to a list of titles (for legacy/advanced analysis)"""
        result = []
        for node in subtopics_tree:
            result.append(node.get('title', ''))
            if node.get('sub_topics'):
                result.extend(self._flatten_subtopics(node['sub_topics']))
        return result

    def _run_analysis_with_progress(self, topic_data, analysis_config, progress_callback, is_cancelled):
        """Run analysis with enhanced progress reporting and ETA updates"""
        try:
            analysis_type = analysis_config.get("type", "iterative")

            # Step 0: Initialize
            progress_callback(0, "Initializing analysis framework...",
                            f"Setting up {analysis_type} analysis for '{topic_data.get('title', 'Unknown')}'")

            if is_cancelled():
                return {"error": "Analysis cancelled by user"}

            # Prepare data structures
            sub_topics_tree = topic_data.get("sub_topics_tree", [])
            flat_sub_topics = self._flatten_subtopics(sub_topics_tree)
            patched_topic_data = dict(topic_data)
            patched_topic_data["sub_topics"] = flat_sub_topics

            # Step 1: Main analysis phase
            if analysis_type == "iterative":
                progress_callback(1, "Analyzing sub-topics sequentially...",
                               f"Processing {len(flat_sub_topics)} sub-topics")
                results = self._run_iterative_with_progress(patched_topic_data, analysis_config, progress_callback, is_cancelled)
            elif analysis_type == "recursive":
                progress_callback(1, "Starting recursive analysis...",
                               f"Depth limit: {analysis_config.get('max_depth', 3)}")
                results = self._run_recursive_with_progress(topic_data, analysis_config, progress_callback, is_cancelled)
            elif analysis_type in ["comparative", "swot", "temporal"]:
                progress_callback(1, f"Running {analysis_type} analysis...",
                               f"Processing {len(flat_sub_topics)} topics")
                results = self._run_advanced_with_progress(patched_topic_data, analysis_config, progress_callback, is_cancelled)
            else:
                return {"error": "Unknown analysis type"}

            if is_cancelled():
                return {"error": "Analysis cancelled by user"}

            # Step 2: Connection discovery (if not handled in main analysis)
            if analysis_type == "iterative":
                progress_callback(2, "Discovering topic connections...",
                               "Analyzing relationships between topics")
                # Connection finding is handled in the analysis functions

            if is_cancelled():
                return {"error": "Analysis cancelled by user"}

            # Final step: Finalize results
            final_step = len(self._get_analysis_steps(analysis_type)) - 1
            progress_callback(final_step, "Finalizing results...",
                            "Preparing analysis output")

            # Display results on main thread
            self.root.after(0, lambda: self.display_results(results))
            return results

        except Exception as e:
            self.logger.error(f"Analysis failed: {e}")
            self.root.after(0, lambda: show_error("Analysis Error", f"Analysis failed: {str(e)}"))
            return {"error": str(e)}
    
    def start_analysis_thread(self, topic_data: Dict[str, Any], analysis_config: Dict[str, Any]):
        """Start analysis in background thread"""
        def run_analysis():
            try:
                self.analysis_running = True
                self.root.after(0, lambda: self.update_status("Starting analysis..."))
                self.root.after(0, lambda: self.update_progress(10))
                
                # Perform analysis based on type
                analysis_type = analysis_config.get("type", "iterative")

                if analysis_type == "iterative":
                    results = self.run_iterative_analysis(topic_data, analysis_config)
                elif analysis_type == "recursive":
                    results = self.run_recursive_analysis(topic_data, analysis_config)
                elif analysis_type in ["comparative", "swot", "temporal"]:
                    results = self.run_advanced_analysis(topic_data, analysis_config)
                else:
                    results = {"error": "Unknown analysis type"}
                
                # Update UI with results
                self.root.after(0, lambda: self.display_results(results))
                self.root.after(0, lambda: self.update_status("Analysis completed"))
                self.root.after(0, lambda: self.update_progress(100))
                
            except Exception as e:
                self.logger.error(f"Analysis failed: {e}")
                self.root.after(0, lambda: show_error("Analysis Error", f"Analysis failed: {str(e)}"))
                self.root.after(0, lambda: self.update_status("Analysis failed"))
            finally:
                self.analysis_running = False
                self.root.after(0, lambda: self.update_progress(0))
        
        threading.Thread(target=run_analysis, daemon=True).start()
    
    def run_iterative_analysis(self, topic_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Run enhanced iterative analysis with context awareness"""

        # Check if enhanced analysis is enabled
        use_enhanced = config.get("use_enhanced_analysis", True)

        if use_enhanced:
            return self.run_enhanced_analysis(topic_data, config)
        else:
            return self.run_legacy_analysis(topic_data, config)

    def run_enhanced_analysis(self, topic_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Run enhanced context-aware analysis (now uses sub_topics_tree)"""
        main_topic = topic_data.get("title", "")
        sub_topics_tree = topic_data.get("sub_topics_tree", [])
        # Use enhanced client for progressive analysis (pass the tree)
        results = self.enhanced_client.progressive_analysis(
            main_topic=main_topic,
            sub_topics_tree=sub_topics_tree,
            config=config
        )
        return results

    def run_legacy_analysis(self, topic_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Run legacy iterative analysis (original implementation, now uses flat sub_topics from tree)"""
        results = {
            "type": "iterative",
            "topic": topic_data.get("title", ""),
            "sub_analyses": [],
            "connections": [],
            "metadata": {
                "model": config.get("model", self.ollama_client.default_model),
                "timestamp": self.get_timestamp()
            }
        }
        main_topic = topic_data.get("title", "Analysis Topics")
        sub_topics = topic_data.get("sub_topics", [])
        sub_topics_detailed = topic_data.get("sub_topics_detailed", [])

        # Skip main topic analysis since we're using Topic Library instead
        # Analyze sub-topics iteratively with descriptions
        total_subtopics = len(sub_topics_detailed) if sub_topics_detailed else len(sub_topics)

        # Use detailed sub-topics if available, otherwise fall back to simple list
        topics_to_analyze = sub_topics_detailed if sub_topics_detailed else [{'title': topic, 'description': ''} for topic in sub_topics]

        for i, sub_topic_info in enumerate(topics_to_analyze):
            if not self.analysis_running:
                break

            # Extract title and description
            if isinstance(sub_topic_info, dict):
                topic_title = sub_topic_info.get('title', '')
                topic_description = sub_topic_info.get('description', '')
            else:
                # Fallback for simple string topics
                topic_title = str(sub_topic_info)
                topic_description = ''

            # Create context that includes the description
            context_parts = []
            if topic_description:
                context_parts.append(f"Description: {topic_description}")
            if main_topic and main_topic != "Analysis Topics":
                context_parts.append(f"Main context: {main_topic}")

            context = "; ".join(context_parts) if context_parts else None

            analysis = self.ollama_client.analyze_topic(topic_title, context=context)
            if analysis:
                results["sub_analyses"].append({
                    "topic": topic_title,
                    "description": topic_description,
                    "analysis": analysis
                })

        # Find connections using topic titles
        topic_titles = [info.get('title', str(info)) if isinstance(info, dict) else str(info) for info in topics_to_analyze]
        if len(topic_titles) > 1:
            connections = self.ollama_client.find_connections(topic_titles)
            if connections:
                results["connections"] = connections
        return results

    def run_recursive_analysis(self, topic_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Run recursive analysis"""
        results = {
            "type": "recursive",
            "topic": topic_data.get("title", ""),
            "analysis_tree": {},
            "metadata": {
                "model": config.get("model", self.ollama_client.default_model),
                "max_depth": config.get("max_depth", 3),
                "timestamp": self.get_timestamp()
            }
        }

        main_topic = topic_data.get("title", "")
        max_depth = config.get("max_depth", 3)

        # Start recursive analysis
        results["analysis_tree"] = self.analyze_recursive(main_topic, 0, max_depth)

        return results

    def analyze_recursive(self, topic: str, current_depth: int, max_depth: int) -> Dict[str, Any]:
        """Recursively analyze a topic"""
        if current_depth >= max_depth or not self.analysis_running:
            return {"topic": topic, "analysis": None, "subtopics": []}

        progress = 20 + (70 * current_depth / max_depth)
        self.root.after(0, lambda p=progress: self.update_progress(p))
        self.root.after(0, lambda t=topic: self.update_status(f"Analyzing (depth {current_depth}): {t}"))

        # Analyze current topic
        analysis = self.ollama_client.analyze_topic(topic, analysis_type="detailed")

        # Generate subtopics for next level
        subtopic_prompt = f"Based on the topic '{topic}', suggest 3-5 related subtopics for deeper analysis. Return only the subtopic names, one per line."
        subtopics_text = self.ollama_client.generate(subtopic_prompt)

        subtopics = []
        if subtopics_text:
            subtopic_lines = [line.strip() for line in subtopics_text.split('\n') if line.strip()]
            subtopics = subtopic_lines[:5]  # Limit to 5 subtopics

        # Recursively analyze subtopics
        subtopic_analyses = []
        for subtopic in subtopics:
            if self.analysis_running:
                subtopic_analysis = self.analyze_recursive(subtopic, current_depth + 1, max_depth)
                subtopic_analyses.append(subtopic_analysis)

        return {
            "topic": topic,
            "analysis": analysis,
            "subtopics": subtopic_analyses,
            "depth": current_depth
        }

    def run_advanced_analysis(self, topic_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Run advanced analysis types (comparative, SWOT, temporal) using sub-topics with descriptions"""
        analysis_type = config.get("type", "comparative")
        try:
            # Get sub-topics with descriptions
            sub_topics_detailed = topic_data.get("sub_topics_detailed", [])
            sub_topics = topic_data.get("sub_topics", [])

            # Create context from sub-topic descriptions
            context_parts = []
            if sub_topics_detailed:
                for topic_info in sub_topics_detailed:
                    if isinstance(topic_info, dict) and topic_info.get('description'):
                        context_parts.append(f"{topic_info['title']}: {topic_info['description']}")

            combined_context = "; ".join(context_parts) if context_parts else topic_data.get("description", "")

            # Prepare data for advanced analysis
            if analysis_type == "comparative":
                topics_list = [info.get('title', str(info)) if isinstance(info, dict) else str(info)
                              for info in (sub_topics_detailed if sub_topics_detailed else sub_topics)]
                analysis_data = {
                    "topics": topics_list or [topic_data.get("title", "Analysis Topics")],
                    "context": combined_context
                }
            elif analysis_type == "swot":
                # For SWOT, use the first sub-topic or main topic
                main_topic = ""
                if sub_topics_detailed:
                    main_topic = sub_topics_detailed[0].get('title', '') if sub_topics_detailed else ""
                elif sub_topics:
                    main_topic = sub_topics[0] if sub_topics else ""
                else:
                    main_topic = topic_data.get("title", "Analysis Topics")

                analysis_data = {
                    "topic": main_topic,
                    "context": combined_context
                }
            elif analysis_type == "temporal":
                # For temporal, use the first sub-topic or main topic
                main_topic = ""
                if sub_topics_detailed:
                    main_topic = sub_topics_detailed[0].get('title', '') if sub_topics_detailed else ""
                elif sub_topics:
                    main_topic = sub_topics[0] if sub_topics else ""
                else:
                    main_topic = topic_data.get("title", "Analysis Topics")

                analysis_data = {
                    "topic": main_topic,
                    "time_frame": "10 years",
                    "context": combined_context
                }
            else:
                return {"error": f"Unsupported advanced analysis type: {analysis_type}"}
            results = analysis_registry.analyze(analysis_type, analysis_data, self.ollama_client)
            if "metadata" not in results:
                results["metadata"] = {}
            results["metadata"].update({
                "model": config.get("model", self.ollama_client.default_model),
                "timestamp": self.get_timestamp(),
                "original_topic_data": topic_data
            })
            return results
        except Exception as e:
            self.logger.error(f"Advanced analysis failed: {e}")
            return {
                "error": f"Advanced analysis failed: {str(e)}",
                "type": analysis_type
            }

    def get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def display_results(self, results: Dict[str, Any]):
        """Display analysis results"""
        self.results_viewer.display_results(results)
    
    # Menu command implementations
    def new_analysis(self):
        """Create new analysis"""
        if self.analysis_running:
            show_error("Analysis Running", "Cannot create new analysis while one is running.")
            return
        
        if confirm_action("New Analysis", "Clear current analysis and start new?"):
            self.clear_all()
    
    def open_analysis(self):
        """Open saved analysis"""
        filename = filedialog.askopenfilename(
            title="Open Analysis",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            # Implementation for loading analysis
            show_info("Open Analysis", f"Opening: {filename}")
    
    def save_analysis(self):
        """Save current analysis"""
        filename = filedialog.asksaveasfilename(
            title="Save Analysis",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            # Implementation for saving analysis
            show_info("Save Analysis", f"Saving: {filename}")
    
    def export_results(self):
        """Export analysis results"""
        if not self.results_viewer.has_results():
            show_error("No Results", "No analysis results to export.")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Export Results",
            defaultextension=".md",
            filetypes=[("Markdown files", "*.md"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.results_viewer.export_results(filename)
    
    def clear_all(self):
        """Clear all data"""
        self.topic_input.clear()
        self.analysis_panel.reset()
        self.results_viewer.clear()
        self.update_status("Ready")
    
    def show_settings(self):
        """Show settings dialog"""
        SettingsDialog(self.root, self.config)
    
    def start_analysis(self):
        """Start analysis from menu"""
        self.analysis_panel.start_analysis()
    
    def stop_analysis(self):
        """Stop current analysis"""
        if self.analysis_running:
            self.analysis_running = False
            self.update_status("Analysis stopped")
    
    def view_connections(self):
        """View connection analysis"""
        show_info("Connections", "Connection viewer will be implemented")
    
    def show_about(self):
        """Show about dialog"""
        about_text = """AI Analysis Program v1.0
        
A comprehensive tool for topical AI analysis using Ollama API.

Features:
- Iterative and recursive analysis
- Connection finding
- Interactive results visualization
- Markdown export

Built with Python and Tkinter."""
        
        messagebox.showinfo("About", about_text)
    
    def show_user_guide(self):
        """Show user guide"""
        show_info("User Guide", "User guide will be implemented")
    
    def confirm_close(self) -> bool:
        """Confirm application closing"""
        if self.analysis_running:
            return confirm_action("Exit", "Analysis is running. Exit anyway?")
        return True
    
    def cleanup(self):
        """Cleanup resources"""
        self.analysis_running = False
        if self.topic_input:
            self.topic_input.cleanup()
        if self.analysis_panel:
            self.analysis_panel.cleanup()
        if self.results_viewer:
            self.results_viewer.cleanup()
    
    def on_closing(self):
        """Handle window closing"""
        if self.confirm_close():
            self.root.quit()
