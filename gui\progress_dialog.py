"""
Enhanced progress dialog for long-running analysis operations
Provides real-time progress updates, intelligent ETA prediction, and cancellation capability
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from typing import Callable, Optional, Any, Dict
from datetime import datetime, timedelta
from pathlib import Path

from utils.logging_setup import LoggerMixin
from analysis.eta_predictor import ETAPredictor, ETAEstimate

class ProgressDialog(LoggerMixin):
    """Enhanced progress dialog with intelligent ETA prediction"""

    def __init__(self, parent: tk.Widget, title: str = "Analysis Progress", analysis_params: Dict[str, Any] = None):
        self.parent = parent
        self.title = title
        self.cancelled = False
        self.completed = False
        self.result = None
        self.error = None

        # Progress tracking
        self.start_time = None
        self.current_step = 0
        self.total_steps = 0
        self.step_descriptions = []
        self.phase_start_times = {}  # Track when each phase starts

        # ETA prediction
        self.eta_predictor = ETAPredictor()
        self.analysis_params = analysis_params or {}
        self.initial_eta_estimate = None
        self.current_eta_estimate = None
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Setup UI
        self.setup_ui()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def center_dialog(self):
        """Center the dialog on screen"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"500x300+{x}+{y}")
    
    def setup_ui(self):
        """Set up the progress dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title label
        self.title_label = ttk.Label(
            main_frame,
            text="Preparing analysis...",
            font=('Arial', 12, 'bold')
        )
        self.title_label.pack(pady=(0, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(pady=(0, 10))
        
        # Progress percentage
        self.percentage_label = ttk.Label(main_frame, text="0%")
        self.percentage_label.pack()
        
        # Current step description
        self.step_label = ttk.Label(
            main_frame,
            text="Initializing...",
            wraplength=450,
            justify=tk.CENTER
        )
        self.step_label.pack(pady=(10, 0))
        
        # Enhanced time information frame
        time_frame = ttk.LabelFrame(main_frame, text="Time Information", padding=10)
        time_frame.pack(pady=(20, 0), fill=tk.X)

        # First row: Elapsed and Remaining
        time_row1 = ttk.Frame(time_frame)
        time_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(time_row1, text="Elapsed:").pack(side=tk.LEFT)
        self.elapsed_label = ttk.Label(time_row1, text="00:00", font=('Arial', 10, 'bold'))
        self.elapsed_label.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(time_row1, text="Remaining:").pack(side=tk.LEFT)
        self.remaining_label = ttk.Label(time_row1, text="--:--", font=('Arial', 10, 'bold'))
        self.remaining_label.pack(side=tk.LEFT, padx=(5, 0))

        # Second row: ETA and Confidence
        time_row2 = ttk.Frame(time_frame)
        time_row2.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(time_row2, text="ETA:").pack(side=tk.LEFT)
        self.eta_label = ttk.Label(time_row2, text="--:--", font=('Arial', 9))
        self.eta_label.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(time_row2, text="Confidence:").pack(side=tk.LEFT)
        self.confidence_label = ttk.Label(time_row2, text="--", font=('Arial', 9))
        self.confidence_label.pack(side=tk.LEFT, padx=(5, 0))

        # Phase progress bar (for current phase)
        phase_frame = ttk.Frame(time_frame)
        phase_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(phase_frame, text="Current Phase:").pack(anchor=tk.W)
        self.phase_progress_var = tk.DoubleVar()
        self.phase_progress_bar = ttk.Progressbar(
            phase_frame,
            variable=self.phase_progress_var,
            mode='determinate',
            length=300
        )
        self.phase_progress_bar.pack(fill=tk.X, pady=(2, 0))
        
        # Details text area
        details_frame = ttk.LabelFrame(main_frame, text="Details", padding=5)
        details_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # Scrollable text widget
        text_frame = ttk.Frame(details_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.details_text = tk.Text(
            text_frame,
            height=6,
            wrap=tk.WORD,
            yscrollcommand=scrollbar.set,
            font=('Courier', 9),
            state=tk.DISABLED
        )
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.details_text.yview)
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Cancel button
        self.cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel_operation
        )
        self.cancel_button.pack(side=tk.RIGHT)
        
        # Hide in background button
        self.hide_button = ttk.Button(
            button_frame,
            text="Hide",
            command=self.hide_dialog
        )
        self.hide_button.pack(side=tk.RIGHT, padx=(0, 5))
        
        # Start time updates
        self.start_time = datetime.now()
        self.update_time_display()

        # Initialize ETA prediction if analysis parameters are available
        if self.analysis_params:
            self._initialize_eta_prediction()
    
    def set_steps(self, step_descriptions: list):
        """Set the steps for the progress dialog"""
        self.step_descriptions = step_descriptions
        self.total_steps = len(step_descriptions)
        self.current_step = 0

        if self.total_steps > 0:
            self.step_label.config(text=self.step_descriptions[0])

        # Update ETA prediction with step information
        if self.analysis_params and self.eta_predictor:
            self._update_eta_prediction()

    def _initialize_eta_prediction(self):
        """Initialize ETA prediction based on analysis parameters"""
        try:
            analysis_type = self.analysis_params.get("type", "iterative")
            model = self.analysis_params.get("model", "llama2")
            topic_count = self.analysis_params.get("topic_count", 1)

            self.initial_eta_estimate = self.eta_predictor.estimate_analysis_time(
                analysis_type=analysis_type,
                model=model,
                topic_count=topic_count,
                additional_params=self.analysis_params
            )

            self.current_eta_estimate = self.initial_eta_estimate
            self._update_eta_display()

        except Exception as e:
            self.logger.error(f"Failed to initialize ETA prediction: {e}")

    def _update_eta_prediction(self):
        """Update ETA prediction based on current progress"""
        if not self.initial_eta_estimate or self.current_step == 0:
            return

        try:
            # Calculate actual vs predicted progress
            elapsed_seconds = (datetime.now() - self.start_time).total_seconds()
            expected_progress = self.current_step / self.total_steps

            if expected_progress > 0:
                # Adjust remaining time based on actual performance
                actual_rate = expected_progress / elapsed_seconds
                predicted_rate = 1.0 / self.initial_eta_estimate.estimated_seconds

                performance_factor = actual_rate / predicted_rate if predicted_rate > 0 else 1.0

                # Update remaining time estimate
                remaining_progress = 1.0 - expected_progress
                adjusted_remaining_time = (remaining_progress / actual_rate) if actual_rate > 0 else 0

                # Create updated estimate
                self.current_eta_estimate = ETAEstimate(
                    estimated_seconds=adjusted_remaining_time,
                    confidence_level=min(0.9, self.initial_eta_estimate.confidence_level + 0.1),
                    breakdown=self.initial_eta_estimate.breakdown,
                    factors_considered=self.initial_eta_estimate.factors_considered + ["Real-time adjustment"],
                    method_used="adaptive"
                )

                self._update_eta_display()

        except Exception as e:
            self.logger.error(f"Failed to update ETA prediction: {e}")

    def _update_eta_display(self):
        """Update the ETA display elements"""
        if not self.current_eta_estimate:
            return

        try:
            # Format ETA time
            eta_seconds = self.current_eta_estimate.estimated_seconds
            if eta_seconds > 0:
                eta_time = timedelta(seconds=int(eta_seconds))
                eta_str = str(eta_time).split('.')[0]

                # Calculate completion time
                completion_time = datetime.now() + eta_time
                completion_str = completion_time.strftime("%H:%M:%S")

                self.eta_label.config(text=f"{eta_str} ({completion_str})")
            else:
                self.eta_label.config(text="Completing...")

            # Format confidence level
            confidence_pct = int(self.current_eta_estimate.confidence_level * 100)
            confidence_color = "green" if confidence_pct > 70 else "orange" if confidence_pct > 40 else "red"
            self.confidence_label.config(text=f"{confidence_pct}%", foreground=confidence_color)

        except Exception as e:
            self.logger.error(f"Failed to update ETA display: {e}")
    
    def update_progress(self, step: int, description: str = "", details: str = "", phase_progress: float = None):
        """Update progress to specific step with enhanced tracking"""
        if self.cancelled:
            return

        # Track phase timing
        if step != self.current_step:
            current_time = datetime.now()
            if step in range(len(self.step_descriptions)):
                phase_name = self.step_descriptions[step] if step < len(self.step_descriptions) else f"Step {step}"
                self.phase_start_times[step] = current_time

                # Record previous phase completion time
                if self.current_step in self.phase_start_times and self.current_step != step:
                    prev_phase_time = (current_time - self.phase_start_times[self.current_step]).total_seconds()
                    self.add_detail(f"Phase '{self.step_descriptions[self.current_step]}' completed in {prev_phase_time:.1f}s")

        self.current_step = step

        # Update main progress bar
        if self.total_steps > 0:
            progress = (step / self.total_steps) * 100
            self.progress_var.set(progress)
            self.percentage_label.config(text=f"{progress:.1f}%")

        # Update phase progress bar
        if phase_progress is not None:
            self.phase_progress_var.set(phase_progress)
        else:
            # Default phase progress based on step progress
            if self.total_steps > 0:
                phase_prog = ((step % 1) * 100) if step != int(step) else 0
                self.phase_progress_var.set(phase_prog)

        # Update step description
        if description:
            self.step_label.config(text=description)
        elif step < len(self.step_descriptions):
            self.step_label.config(text=self.step_descriptions[step])

        # Add details if provided
        if details:
            self.add_detail(details)

        # Update ETA prediction
        self._update_eta_prediction()

        # Update display
        self.dialog.update_idletasks()
    
    def add_detail(self, detail: str):
        """Add a detail message to the details area"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        message = f"[{timestamp}] {detail}\n"
        
        self.details_text.config(state=tk.NORMAL)
        self.details_text.insert(tk.END, message)
        self.details_text.see(tk.END)
        self.details_text.config(state=tk.DISABLED)
    
    def update_time_display(self):
        """Update elapsed and remaining time display with enhanced ETA"""
        if self.cancelled or self.completed:
            return

        if self.start_time:
            elapsed = datetime.now() - self.start_time
            elapsed_str = str(elapsed).split('.')[0]  # Remove microseconds
            self.elapsed_label.config(text=elapsed_str)

            # Use intelligent ETA if available, otherwise fall back to simple calculation
            if self.current_eta_estimate:
                remaining_seconds = self.current_eta_estimate.estimated_seconds
                if remaining_seconds > 0:
                    remaining_time = timedelta(seconds=int(remaining_seconds))
                    remaining_str = str(remaining_time).split('.')[0]
                    self.remaining_label.config(text=remaining_str)
                else:
                    self.remaining_label.config(text="Completing...")
            else:
                # Fallback to simple linear calculation
                if self.current_step > 0 and self.total_steps > 0:
                    avg_time_per_step = elapsed.total_seconds() / self.current_step
                    remaining_steps = self.total_steps - self.current_step
                    remaining_seconds = avg_time_per_step * remaining_steps

                    if remaining_seconds > 0:
                        remaining_time = timedelta(seconds=int(remaining_seconds))
                        remaining_str = str(remaining_time).split('.')[0]
                        self.remaining_label.config(text=remaining_str)

        # Schedule next update
        self.dialog.after(1000, self.update_time_display)
    
    def cancel_operation(self):
        """Cancel the current operation"""
        if not self.completed:
            self.cancelled = True
            self.title_label.config(text="Cancelling...")
            self.step_label.config(text="Please wait while the operation is cancelled...")
            self.cancel_button.config(state=tk.DISABLED)
            self.add_detail("Cancellation requested by user")
    
    def hide_dialog(self):
        """Hide the dialog (continue in background)"""
        self.dialog.withdraw()
    
    def show_dialog(self):
        """Show the dialog again"""
        self.dialog.deiconify()
        self.dialog.lift()
    
    def complete_operation(self, result: Any = None, error: str = None):
        """Mark operation as complete and record timing data"""
        self.completed = True
        self.result = result
        self.error = error

        # Record timing data for ETA learning
        if self.start_time and self.analysis_params:
            total_time = (datetime.now() - self.start_time).total_seconds()

            try:
                self.eta_predictor.record_analysis_timing(
                    analysis_type=self.analysis_params.get("type", "iterative"),
                    model=self.analysis_params.get("model", "llama2"),
                    topic_count=self.analysis_params.get("topic_count", 1),
                    total_time=total_time,
                    phase_times=self._get_phase_times(),
                    success=(error is None)
                )
            except Exception as e:
                self.logger.error(f"Failed to record timing data: {e}")

        if error:
            self.title_label.config(text="Analysis Failed")
            self.step_label.config(text=f"Error: {error}")
            self.progress_var.set(0)
            self.percentage_label.config(text="Error")
            self.phase_progress_var.set(0)
            self.add_detail(f"Analysis failed: {error}")
            self.eta_label.config(text="--:--")
            self.confidence_label.config(text="--")
        else:
            self.title_label.config(text="Analysis Complete")
            self.step_label.config(text="Analysis completed successfully!")
            self.progress_var.set(100)
            self.percentage_label.config(text="100%")
            self.phase_progress_var.set(100)
            self.add_detail("Analysis completed successfully")
            self.eta_label.config(text="Complete")
            self.confidence_label.config(text="100%", foreground="green")

        # Update buttons
        self.cancel_button.config(text="Close", state=tk.NORMAL, command=self.close_dialog)
        self.hide_button.config(state=tk.DISABLED)

    def _get_phase_times(self) -> Dict[str, float]:
        """Get timing data for each phase"""
        phase_times = {}

        for step, start_time in self.phase_start_times.items():
            if step < len(self.step_descriptions):
                phase_name = self.step_descriptions[step]

                # Calculate phase duration
                if step + 1 in self.phase_start_times:
                    end_time = self.phase_start_times[step + 1]
                else:
                    end_time = datetime.now()

                duration = (end_time - start_time).total_seconds()
                phase_times[phase_name] = duration

        return phase_times
    
    def close_dialog(self):
        """Close the dialog"""
        self.dialog.destroy()
    
    def on_close(self):
        """Handle window close event"""
        if not self.completed:
            # Ask for confirmation if operation is still running
            import tkinter.messagebox as messagebox
            if messagebox.askyesno("Cancel Operation", "Analysis is still running. Cancel it?"):
                self.cancel_operation()
                self.dialog.after(1000, self.close_dialog)  # Close after a delay
        else:
            self.close_dialog()

class AnalysisProgressManager(LoggerMixin):
    """Manages progress for analysis operations"""
    
    def __init__(self, parent: tk.Widget):
        self.parent = parent
        self.current_dialog = None
        self.operation_thread = None
        self.cancelled = False
    
    def start_analysis(self, operation_func: Callable, steps: list, title: str = "Analysis Progress", analysis_params: Dict[str, Any] = None) -> Any:
        """Start an analysis operation with enhanced progress tracking and ETA prediction"""
        # Create enhanced progress dialog with analysis parameters
        self.current_dialog = ProgressDialog(self.parent, title, analysis_params)
        self.current_dialog.set_steps(steps)
        self.cancelled = False
        
        # Progress callback
        def progress_callback(step: int, description: str = "", details: str = ""):
            if self.current_dialog and not self.cancelled:
                self.current_dialog.update_progress(step, description, details)
        
        # Cancellation check
        def is_cancelled() -> bool:
            return self.current_dialog.cancelled if self.current_dialog else False
        
        # Run operation in thread
        def run_operation():
            try:
                result = operation_func(progress_callback, is_cancelled)
                
                if self.current_dialog:
                    if is_cancelled():
                        self.current_dialog.complete_operation(error="Operation cancelled by user")
                    else:
                        self.current_dialog.complete_operation(result)
                
                return result
                
            except Exception as e:
                self.logger.error(f"Analysis operation failed: {e}")
                if self.current_dialog:
                    self.current_dialog.complete_operation(error=str(e))
                return None
        
        # Start operation thread
        self.operation_thread = threading.Thread(target=run_operation, daemon=True)
        self.operation_thread.start()
        
        # Return dialog for external control
        return self.current_dialog
    
    def is_running(self) -> bool:
        """Check if an analysis is currently running"""
        return (self.operation_thread and 
                self.operation_thread.is_alive() and 
                self.current_dialog and 
                not self.current_dialog.completed)
    
    def cancel_current(self):
        """Cancel current operation"""
        if self.current_dialog:
            self.current_dialog.cancel_operation()
    
    def cleanup(self):
        """Cleanup resources"""
        if self.current_dialog:
            self.current_dialog.close_dialog()
        
        if self.operation_thread and self.operation_thread.is_alive():
            self.cancelled = True
