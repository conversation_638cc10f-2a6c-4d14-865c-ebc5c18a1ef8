#!/usr/bin/env python3
"""
Test script to validate the modifications made to the Deep Research Tool GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.helpers import validate_topic_structure

def test_topic_validation():
    """Test the updated topic validation function"""
    print("Testing topic validation...")
    
    # Test 1: Topic Library entry (should require title and description)
    library_topic = {
        'title': 'Test Topic',
        'description': 'Test description',
        'category': 'General',
        'id': 'test-id'
    }
    assert validate_topic_structure(library_topic), "Library topic validation failed"
    print("✓ Library topic validation passed")
    
    # Test 2: Analysis topic with sub-topics only (no main title required)
    analysis_topic = {
        'title': 'Analysis Topics',
        'description': 'Analysis of selected sub-topics',
        'sub_topics_detailed': [
            {'title': 'Sub-topic 1', 'description': 'Description 1'},
            {'title': 'Sub-topic 2', 'description': 'Description 2'}
        ],
        'sub_topics': ['Sub-topic 1', 'Sub-topic 2']
    }
    assert validate_topic_structure(analysis_topic), "Analysis topic validation failed"
    print("✓ Analysis topic validation passed")
    
    # Test 3: Topic with sub_topics_tree structure
    tree_topic = {
        'title': 'Tree Topic',
        'sub_topics_tree': [
            {
                'title': 'Parent Topic',
                'description': 'Parent description',
                'sub_topics': [
                    {'title': 'Child Topic', 'description': 'Child description'}
                ]
            }
        ]
    }
    assert validate_topic_structure(tree_topic), "Tree topic validation failed"
    print("✓ Tree topic validation passed")
    
    # Test 4: Invalid topic (no title and no sub-topics)
    invalid_topic = {
        'description': 'Just a description'
    }
    assert not validate_topic_structure(invalid_topic), "Invalid topic should fail validation"
    print("✓ Invalid topic correctly rejected")

def test_topic_data_structure():
    """Test the new topic data structure"""
    print("\nTesting topic data structure...")
    
    # Simulate the new get_topic_data output
    topic_data = {
        'title': 'Analysis Topics',
        'description': 'Analysis of selected sub-topics',
        'sub_topics_tree': [
            {
                'title': 'Machine Learning',
                'description': 'Study of algorithms that improve through experience',
                'sub_topics': [
                    {
                        'title': 'Neural Networks',
                        'description': 'Computing systems inspired by biological neural networks'
                    }
                ]
            }
        ],
        'sub_topics': ['Machine Learning', 'Neural Networks'],
        'sub_topics_detailed': [
            {
                'title': 'Machine Learning',
                'description': 'Study of algorithms that improve through experience'
            },
            {
                'title': 'Neural Networks', 
                'description': 'Computing systems inspired by biological neural networks'
            }
        ]
    }
    
    # Validate the structure
    assert validate_topic_structure(topic_data), "New topic data structure validation failed"
    print("✓ New topic data structure validation passed")
    
    # Test that sub_topics_detailed contains descriptions
    assert all('description' in item for item in topic_data['sub_topics_detailed']), "Sub-topics missing descriptions"
    print("✓ Sub-topics contain descriptions")
    
    # Test backward compatibility with sub_topics list
    assert len(topic_data['sub_topics']) == len(topic_data['sub_topics_detailed']), "Sub-topics lists length mismatch"
    print("✓ Backward compatibility maintained")

def test_analysis_context():
    """Test that analysis context includes descriptions"""
    print("\nTesting analysis context...")
    
    # Simulate sub-topics with descriptions
    sub_topics_detailed = [
        {
            'title': 'Artificial Intelligence',
            'description': 'Intelligence demonstrated by machines, in contrast to natural intelligence'
        },
        {
            'title': 'Machine Learning',
            'description': 'Study of algorithms that improve automatically through experience'
        }
    ]
    
    # Test context creation (simulating the analysis logic)
    context_parts = []
    for topic_info in sub_topics_detailed:
        if topic_info.get('description'):
            context_parts.append(f"{topic_info['title']}: {topic_info['description']}")
    
    combined_context = "; ".join(context_parts)
    
    assert "Intelligence demonstrated by machines" in combined_context, "Context missing AI description"
    assert "algorithms that improve automatically" in combined_context, "Context missing ML description"
    print("✓ Analysis context includes descriptions")

if __name__ == "__main__":
    print("Running Deep Research Tool GUI modification tests...\n")
    
    try:
        test_topic_validation()
        test_topic_data_structure()
        test_analysis_context()
        
        print("\n" + "="*50)
        print("✅ ALL TESTS PASSED!")
        print("="*50)
        print("\nModifications summary:")
        print("1. ✓ Removed Main Topic from GUI")
        print("2. ✓ Sub-Topic addition functionality verified")
        print("3. ✓ Analysis updated to use sub-topic descriptions")
        print("4. ✓ Topic data structure updated for sub-topics only")
        print("5. ✓ Validation updated for new structure")
        
    except AssertionError as e:
        print(f"\n❌ TEST FAILED: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        sys.exit(1)
