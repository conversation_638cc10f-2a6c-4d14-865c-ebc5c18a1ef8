"""
Simple validation script for the ETA prediction system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from analysis.eta_predictor import ETAPredictor, AnalysisProfile, ETAEstimate
import tempfile
from pathlib import Path

def test_eta_system():
    """Test the ETA prediction system"""
    print("🧪 Testing ETA Prediction System...")
    
    # Create temporary directory for testing
    test_dir = Path(tempfile.mkdtemp())
    predictor = ETAPredictor(test_dir)
    
    print("✅ ETAPredictor initialized successfully")
    
    # Test 1: Default estimation
    print("\n📊 Test 1: Default Estimation")
    estimate = predictor.estimate_analysis_time(
        analysis_type="iterative",
        model="llama2",
        topic_count=3
    )
    
    print(f"   Estimated time: {estimate.estimated_seconds:.1f} seconds")
    print(f"   Confidence: {estimate.confidence_level:.1%}")
    print(f"   Method: {estimate.method_used}")
    print(f"   Factors: {', '.join(estimate.factors_considered[:2])}")
    
    # Test 2: Different analysis types
    print("\n📊 Test 2: Different Analysis Types")
    types = ["iterative", "recursive", "comparative", "swot", "temporal"]
    
    for analysis_type in types:
        est = predictor.estimate_analysis_time(
            analysis_type=analysis_type,
            model="llama2",
            topic_count=3
        )
        print(f"   {analysis_type:12}: {est.estimated_seconds:6.1f}s (confidence: {est.confidence_level:.1%})")
    
    # Test 3: Model performance factors
    print("\n📊 Test 3: Model Performance Factors")
    models = ["llama2", "llama3", "phi", "mistral"]
    
    for model in models:
        est = predictor.estimate_analysis_time(
            analysis_type="iterative",
            model=model,
            topic_count=3
        )
        print(f"   {model:8}: {est.estimated_seconds:6.1f}s")
    
    # Test 4: Topic count scaling
    print("\n📊 Test 4: Topic Count Scaling")
    topic_counts = [1, 3, 5, 10, 20]
    
    for count in topic_counts:
        est = predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=count
        )
        print(f"   {count:2} topics: {est.estimated_seconds:6.1f}s")
    
    # Test 5: Learning from recorded data
    print("\n📊 Test 5: Learning from Recorded Data")
    
    # Record some timing data
    print("   Recording timing data...")
    for i in range(3):
        predictor.record_analysis_timing(
            analysis_type="iterative",
            model="llama2",
            topic_count=3,
            total_time=45.0 + i * 5,
            success=True
        )
    
    # Get new estimate
    learned_estimate = predictor.estimate_analysis_time(
        analysis_type="iterative",
        model="llama2",
        topic_count=3
    )
    
    print(f"   After learning - Time: {learned_estimate.estimated_seconds:.1f}s")
    print(f"   Method: {learned_estimate.method_used}")
    print(f"   Confidence: {learned_estimate.confidence_level:.1%}")
    
    # Test 6: Phase breakdown
    print("\n📊 Test 6: Phase Time Breakdown")
    total_time = 120.0
    phases = predictor.estimate_phase_times("iterative", total_time)
    
    print(f"   Total estimated time: {total_time}s")
    for phase, time_est in phases.items():
        percentage = (time_est / total_time) * 100
        print(f"   {phase:15}: {time_est:5.1f}s ({percentage:4.1f}%)")
    
    # Test 7: Parameter adjustments
    print("\n📊 Test 7: Parameter Adjustments")
    
    base_est = predictor.estimate_analysis_time("iterative", "llama2", 3)
    
    high_temp_est = predictor.estimate_analysis_time(
        "iterative", "llama2", 3, 
        {"temperature": 0.9}
    )
    
    parallel_est = predictor.estimate_analysis_time(
        "iterative", "llama2", 3,
        {"parallel_processing": True}
    )
    
    cached_est = predictor.estimate_analysis_time(
        "iterative", "llama2", 3,
        {"enable_caching": True}
    )
    
    print(f"   Base estimate:      {base_est.estimated_seconds:6.1f}s")
    print(f"   High temperature:   {high_temp_est.estimated_seconds:6.1f}s")
    print(f"   Parallel processing:{parallel_est.estimated_seconds:6.1f}s")
    print(f"   With caching:       {cached_est.estimated_seconds:6.1f}s")
    
    print("\n✅ All ETA tests completed successfully!")
    print(f"📁 Test data stored in: {test_dir}")
    
    return True

if __name__ == "__main__":
    try:
        test_eta_system()
        print("\n🎉 ETA Prediction System is working correctly!")
    except Exception as e:
        print(f"\n❌ Error testing ETA system: {e}")
        import traceback
        traceback.print_exc()
