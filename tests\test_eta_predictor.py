"""
Test suite for the ETA prediction system
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import time
import json

from analysis.eta_predictor import ETAPredictor, AnalysisProfile, ETAEstimate


class TestETAPredictor(unittest.TestCase):
    """Test cases for ETA prediction functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.predictor = ETAPredictor(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir)
    
    def test_default_estimation(self):
        """Test ETA estimation with default values"""
        estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=3
        )
        
        self.assertIsInstance(estimate, ETAEstimate)
        self.assertGreater(estimate.estimated_seconds, 0)
        self.assertEqual(estimate.method_used, "default_estimates")
        self.assertIn("Default estimates", estimate.factors_considered[0])
    
    def test_parameter_adjustments(self):
        """Test that additional parameters affect estimates"""
        base_estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=3
        )
        
        # Test temperature adjustment
        high_temp_estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=3,
            additional_params={"temperature": 0.9}
        )
        
        self.assertGreater(high_temp_estimate.estimated_seconds, base_estimate.estimated_seconds)
        
        # Test parallel processing adjustment
        parallel_estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=3,
            additional_params={"parallel_processing": True}
        )
        
        self.assertLess(parallel_estimate.estimated_seconds, base_estimate.estimated_seconds)
    
    def test_model_performance_factors(self):
        """Test that different models have different time estimates"""
        llama2_estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=3
        )
        
        phi_estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="phi",
            topic_count=3
        )
        
        # Phi should be faster than llama2 based on model factors
        self.assertLess(phi_estimate.estimated_seconds, llama2_estimate.estimated_seconds)
    
    def test_analysis_type_differences(self):
        """Test that different analysis types have different estimates"""
        iterative_estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=3
        )
        
        recursive_estimate = self.predictor.estimate_analysis_time(
            analysis_type="recursive",
            model="llama2",
            topic_count=3
        )
        
        temporal_estimate = self.predictor.estimate_analysis_time(
            analysis_type="temporal",
            model="llama2",
            topic_count=3
        )
        
        # Recursive and temporal should generally take longer
        self.assertGreater(recursive_estimate.estimated_seconds, iterative_estimate.estimated_seconds)
        self.assertGreater(temporal_estimate.estimated_seconds, iterative_estimate.estimated_seconds)
    
    def test_timing_recording_and_learning(self):
        """Test that the system learns from recorded timings"""
        # Record some timing data
        self.predictor.record_analysis_timing(
            analysis_type="iterative",
            model="llama2",
            topic_count=3,
            total_time=45.0,
            success=True
        )
        
        self.predictor.record_analysis_timing(
            analysis_type="iterative",
            model="llama2",
            topic_count=3,
            total_time=50.0,
            success=True
        )
        
        # Get estimate - should now use historical data
        estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=3
        )
        
        # Should be using historical data or profile-based method
        self.assertIn(estimate.method_used, ["historical_data", "profile_based"])
    
    def test_profile_creation_and_updates(self):
        """Test that profiles are created and updated correctly"""
        # Record multiple timings to create a profile
        for i in range(5):
            self.predictor.record_analysis_timing(
                analysis_type="iterative",
                model="llama2",
                topic_count=2,
                total_time=30.0 + i * 2,  # Varying times
                success=True
            )
        
        # Check that profile was created
        profile_key = "iterative_llama2"
        self.assertIn(profile_key, self.predictor.profiles)
        
        profile = self.predictor.profiles[profile_key]
        self.assertEqual(profile.sample_count, 5)
        self.assertGreater(profile.confidence_level, 0.5)
    
    def test_phase_time_estimation(self):
        """Test phase time breakdown estimation"""
        total_time = 120.0
        
        iterative_phases = self.predictor.estimate_phase_times("iterative", total_time)
        recursive_phases = self.predictor.estimate_phase_times("recursive", total_time)
        
        # Check that phases sum to total time
        self.assertAlmostEqual(sum(iterative_phases.values()), total_time, places=1)
        self.assertAlmostEqual(sum(recursive_phases.values()), total_time, places=1)
        
        # Check that different analysis types have different phase distributions
        self.assertNotEqual(iterative_phases, recursive_phases)
    
    def test_accuracy_statistics(self):
        """Test accuracy statistics calculation"""
        # Record some data
        for i in range(10):
            self.predictor.record_analysis_timing(
                analysis_type="iterative",
                model="llama2",
                topic_count=2,
                total_time=25.0 + i,
                success=True
            )
        
        stats = self.predictor.get_prediction_accuracy()
        
        self.assertIn("iterative", stats)
        self.assertEqual(stats["iterative"]["sample_count"], 10)
        self.assertGreater(stats["iterative"]["confidence"], 0.3)
    
    def test_topic_count_scaling(self):
        """Test that estimates scale appropriately with topic count"""
        small_estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=2
        )
        
        large_estimate = self.predictor.estimate_analysis_time(
            analysis_type="iterative",
            model="llama2",
            topic_count=10
        )
        
        # Larger topic count should take more time
        self.assertGreater(large_estimate.estimated_seconds, small_estimate.estimated_seconds)
        
        # But not linearly (due to scaling factors)
        ratio = large_estimate.estimated_seconds / small_estimate.estimated_seconds
        self.assertGreater(ratio, 2.0)  # More than just linear scaling


if __name__ == "__main__":
    unittest.main()
