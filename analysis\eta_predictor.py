"""
Enhanced ETA (Estimated Time of Arrival) prediction system for analysis operations.
Provides intelligent time estimation based on analysis type, topic count, model performance, and historical data.
"""

import json
import time
import sqlite3
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from statistics import mean, median
import threading

from utils.logging_setup import LoggerMixin


@dataclass
class AnalysisProfile:
    """Profile for a specific type of analysis"""
    analysis_type: str
    model: str
    avg_time_per_topic: float
    base_overhead: float  # Fixed time for initialization, connections, etc.
    topic_scaling_factor: float  # How much each additional topic adds
    confidence_level: float  # 0.0 to 1.0
    sample_count: int  # Number of samples this profile is based on
    last_updated: str


@dataclass
class ETAEstimate:
    """ETA estimation result"""
    estimated_seconds: float
    confidence_level: float
    breakdown: Dict[str, float]  # Time breakdown by phase
    factors_considered: List[str]
    method_used: str


class ETAPredictor(LoggerMixin):
    """Intelligent ETA prediction system for analysis operations"""
    
    def __init__(self, config_dir: Path = None):
        self.config_dir = config_dir or Path("data")
        self.config_dir.mkdir(exist_ok=True)
        
        # Database for historical timing data
        self.db_path = self.config_dir / "eta_history.db"
        self.profiles_path = self.config_dir / "analysis_profiles.json"
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Initialize database and profiles
        self._init_database()
        self.profiles = self._load_profiles()
        
        # Default timing estimates (in seconds) - fallback values
        self.default_timings = {
            "iterative": {"base": 10, "per_topic": 15, "scaling": 1.2},
            "recursive": {"base": 15, "per_topic": 25, "scaling": 1.5},
            "comparative": {"base": 20, "per_topic": 30, "scaling": 1.3},
            "swot": {"base": 25, "per_topic": 20, "scaling": 1.1},
            "temporal": {"base": 30, "per_topic": 35, "scaling": 1.4}
        }
        
        # Model performance factors (relative to base model)
        self.model_factors = {
            "llama2": 1.0,
            "llama3": 0.8,
            "mistral": 0.9,
            "codellama": 1.2,
            "phi": 0.7,
            "gemma": 0.85
        }
    
    def _init_database(self):
        """Initialize SQLite database for timing history"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS timing_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        analysis_type TEXT NOT NULL,
                        model TEXT NOT NULL,
                        topic_count INTEGER NOT NULL,
                        total_time REAL NOT NULL,
                        phase_times TEXT,  -- JSON string of phase timings
                        timestamp TEXT NOT NULL,
                        success BOOLEAN NOT NULL DEFAULT 1
                    )
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_analysis_model 
                    ON timing_history(analysis_type, model)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_timestamp 
                    ON timing_history(timestamp)
                """)
                
        except Exception as e:
            self.logger.error(f"Failed to initialize ETA database: {e}")
    
    def _load_profiles(self) -> Dict[str, AnalysisProfile]:
        """Load analysis profiles from file"""
        try:
            if self.profiles_path.exists():
                with open(self.profiles_path, 'r') as f:
                    data = json.load(f)
                    return {
                        key: AnalysisProfile(**profile_data)
                        for key, profile_data in data.items()
                    }
        except Exception as e:
            self.logger.error(f"Failed to load analysis profiles: {e}")
        
        return {}
    
    def _save_profiles(self):
        """Save analysis profiles to file"""
        try:
            with open(self.profiles_path, 'w') as f:
                data = {
                    key: asdict(profile)
                    for key, profile in self.profiles.items()
                }
                json.dump(data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save analysis profiles: {e}")
    
    def estimate_analysis_time(
        self,
        analysis_type: str,
        model: str,
        topic_count: int,
        additional_params: Dict[str, Any] = None
    ) -> ETAEstimate:
        """
        Estimate total analysis time based on parameters
        
        Args:
            analysis_type: Type of analysis (iterative, recursive, etc.)
            model: AI model being used
            topic_count: Number of topics/sub-topics to analyze
            additional_params: Additional parameters that might affect timing
            
        Returns:
            ETAEstimate with time prediction and confidence level
        """
        additional_params = additional_params or {}
        
        with self._lock:
            # Try to get profile-based estimate first
            profile_key = f"{analysis_type}_{model}"
            
            if profile_key in self.profiles:
                estimate = self._estimate_from_profile(
                    self.profiles[profile_key], topic_count, additional_params
                )
                estimate.method_used = "profile_based"
                return estimate
            
            # Fall back to historical data
            historical_estimate = self._estimate_from_history(
                analysis_type, model, topic_count, additional_params
            )
            if historical_estimate:
                historical_estimate.method_used = "historical_data"
                return historical_estimate
            
            # Final fallback to default estimates
            return self._estimate_from_defaults(
                analysis_type, model, topic_count, additional_params
            )
    
    def _estimate_from_profile(
        self,
        profile: AnalysisProfile,
        topic_count: int,
        additional_params: Dict[str, Any]
    ) -> ETAEstimate:
        """Estimate time using analysis profile"""
        
        # Base calculation
        base_time = profile.base_overhead
        topic_time = profile.avg_time_per_topic * topic_count
        
        # Apply scaling factor for larger analyses
        if topic_count > 5:
            scaling = profile.topic_scaling_factor ** (topic_count - 5)
            topic_time *= scaling
        
        total_time = base_time + topic_time
        
        # Apply additional parameter adjustments
        total_time = self._apply_parameter_adjustments(total_time, additional_params)
        
        # Create breakdown
        breakdown = {
            "initialization": base_time * 0.1,
            "topic_analysis": topic_time,
            "connections": base_time * 0.3,
            "synthesis": base_time * 0.6
        }
        
        factors = [
            f"Analysis type: {profile.analysis_type}",
            f"Model: {profile.model}",
            f"Topic count: {topic_count}",
            f"Profile samples: {profile.sample_count}"
        ]
        
        return ETAEstimate(
            estimated_seconds=total_time,
            confidence_level=profile.confidence_level,
            breakdown=breakdown,
            factors_considered=factors,
            method_used="profile_based"
        )

    def _estimate_from_history(
        self,
        analysis_type: str,
        model: str,
        topic_count: int,
        additional_params: Dict[str, Any]
    ) -> Optional[ETAEstimate]:
        """Estimate time using historical data"""

        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get recent successful analyses of similar type
                cursor = conn.execute("""
                    SELECT total_time, topic_count, phase_times
                    FROM timing_history
                    WHERE analysis_type = ? AND model = ? AND success = 1
                    ORDER BY timestamp DESC
                    LIMIT 20
                """, (analysis_type, model))

                records = cursor.fetchall()

                if not records:
                    return None

                # Calculate average time per topic from historical data
                times_per_topic = []
                for total_time, hist_topic_count, _ in records:
                    if hist_topic_count > 0:
                        times_per_topic.append(total_time / hist_topic_count)

                if not times_per_topic:
                    return None

                avg_time_per_topic = mean(times_per_topic)
                median_time_per_topic = median(times_per_topic)

                # Use median for more stable estimates
                estimated_time = median_time_per_topic * topic_count

                # Add base overhead (estimated from historical data)
                base_overhead = min(record[0] for record in records) * 0.3
                estimated_time += base_overhead

                # Apply parameter adjustments
                estimated_time = self._apply_parameter_adjustments(estimated_time, additional_params)

                # Calculate confidence based on data consistency
                variance = sum((t - avg_time_per_topic) ** 2 for t in times_per_topic) / len(times_per_topic)
                confidence = max(0.3, min(0.9, 1.0 - (variance / avg_time_per_topic)))

                breakdown = {
                    "initialization": base_overhead * 0.2,
                    "topic_analysis": estimated_time * 0.6,
                    "connections": base_overhead * 0.4,
                    "synthesis": base_overhead * 0.4
                }

                factors = [
                    f"Historical samples: {len(records)}",
                    f"Avg time per topic: {avg_time_per_topic:.1f}s",
                    f"Topic count: {topic_count}"
                ]

                return ETAEstimate(
                    estimated_seconds=estimated_time,
                    confidence_level=confidence,
                    breakdown=breakdown,
                    factors_considered=factors,
                    method_used="historical_data"
                )

        except Exception as e:
            self.logger.error(f"Failed to estimate from history: {e}")
            return None

    def _estimate_from_defaults(
        self,
        analysis_type: str,
        model: str,
        topic_count: int,
        additional_params: Dict[str, Any]
    ) -> ETAEstimate:
        """Estimate time using default values (fallback)"""

        # Get default timing for analysis type
        defaults = self.default_timings.get(analysis_type, self.default_timings["iterative"])

        base_time = defaults["base"]
        per_topic_time = defaults["per_topic"]
        scaling_factor = defaults["scaling"]

        # Calculate base estimate
        topic_time = per_topic_time * topic_count

        # Apply scaling for larger analyses
        if topic_count > 5:
            scaling = scaling_factor ** (topic_count - 5)
            topic_time *= scaling

        total_time = base_time + topic_time

        # Apply model performance factor
        model_factor = self.model_factors.get(model.lower(), 1.0)
        total_time *= model_factor

        # Apply parameter adjustments
        total_time = self._apply_parameter_adjustments(total_time, additional_params)

        breakdown = {
            "initialization": base_time * 0.2,
            "topic_analysis": topic_time,
            "connections": base_time * 0.4,
            "synthesis": base_time * 0.4
        }

        factors = [
            f"Default estimates for {analysis_type}",
            f"Model factor: {model_factor}",
            f"Topic count: {topic_count}",
            "No historical data available"
        ]

        return ETAEstimate(
            estimated_seconds=total_time,
            confidence_level=0.5,  # Medium confidence for defaults
            breakdown=breakdown,
            factors_considered=factors,
            method_used="default_estimates"
        )

    def _apply_parameter_adjustments(
        self,
        base_time: float,
        additional_params: Dict[str, Any]
    ) -> float:
        """Apply adjustments based on additional parameters"""

        adjusted_time = base_time

        # Temperature adjustment (higher temp = more creative but slower)
        temperature = additional_params.get("temperature", 0.7)
        if temperature > 0.8:
            adjusted_time *= 1.2
        elif temperature < 0.3:
            adjusted_time *= 0.9

        # Max depth adjustment for recursive analysis
        max_depth = additional_params.get("max_depth", 3)
        if max_depth > 3:
            adjusted_time *= (1.0 + (max_depth - 3) * 0.3)

        # Parallel processing adjustment
        if additional_params.get("parallel_processing", False):
            adjusted_time *= 0.7  # Faster with parallel processing

        # Caching adjustment
        if additional_params.get("enable_caching", True):
            adjusted_time *= 0.8  # Faster with caching

        return adjusted_time

    def record_analysis_timing(
        self,
        analysis_type: str,
        model: str,
        topic_count: int,
        total_time: float,
        phase_times: Dict[str, float] = None,
        success: bool = True
    ):
        """Record timing data for future ETA predictions"""

        try:
            with self._lock:
                # Store in database
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT INTO timing_history
                        (analysis_type, model, topic_count, total_time, phase_times, timestamp, success)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        analysis_type,
                        model,
                        topic_count,
                        total_time,
                        json.dumps(phase_times) if phase_times else None,
                        datetime.now().isoformat(),
                        success
                    ))

                # Update or create profile
                self._update_profile(analysis_type, model, topic_count, total_time, success)

                # Clean old records (keep last 1000)
                self._cleanup_old_records()

        except Exception as e:
            self.logger.error(f"Failed to record timing data: {e}")

    def _update_profile(
        self,
        analysis_type: str,
        model: str,
        topic_count: int,
        total_time: float,
        success: bool
    ):
        """Update analysis profile with new timing data"""

        if not success:
            return

        profile_key = f"{analysis_type}_{model}"

        if profile_key in self.profiles:
            profile = self.profiles[profile_key]

            # Update running averages
            old_weight = profile.sample_count
            new_weight = old_weight + 1

            # Update average time per topic
            if topic_count > 0:
                new_time_per_topic = total_time / topic_count
                profile.avg_time_per_topic = (
                    (profile.avg_time_per_topic * old_weight + new_time_per_topic) / new_weight
                )

            # Update base overhead (estimate from single-topic analyses)
            if topic_count == 1:
                profile.base_overhead = (
                    (profile.base_overhead * old_weight + total_time * 0.3) / new_weight
                )

            profile.sample_count = new_weight
            profile.last_updated = datetime.now().isoformat()

            # Adjust confidence based on sample count
            profile.confidence_level = min(0.95, 0.5 + (profile.sample_count * 0.05))

        else:
            # Create new profile
            base_overhead = total_time * 0.3 if topic_count == 1 else 15.0
            avg_time_per_topic = total_time / topic_count if topic_count > 0 else 20.0

            self.profiles[profile_key] = AnalysisProfile(
                analysis_type=analysis_type,
                model=model,
                avg_time_per_topic=avg_time_per_topic,
                base_overhead=base_overhead,
                topic_scaling_factor=1.2,
                confidence_level=0.6,
                sample_count=1,
                last_updated=datetime.now().isoformat()
            )

        # Save updated profiles
        self._save_profiles()

    def _cleanup_old_records(self):
        """Clean up old timing records to prevent database bloat"""

        try:
            with sqlite3.connect(self.db_path) as conn:
                # Keep only the most recent 1000 records
                conn.execute("""
                    DELETE FROM timing_history
                    WHERE id NOT IN (
                        SELECT id FROM timing_history
                        ORDER BY timestamp DESC
                        LIMIT 1000
                    )
                """)

        except Exception as e:
            self.logger.error(f"Failed to cleanup old records: {e}")

    def get_prediction_accuracy(self) -> Dict[str, float]:
        """Get statistics on prediction accuracy"""

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT analysis_type, COUNT(*) as count, AVG(total_time) as avg_time
                    FROM timing_history
                    WHERE success = 1 AND timestamp > datetime('now', '-30 days')
                    GROUP BY analysis_type
                """)

                accuracy_stats = {}
                for analysis_type, count, avg_time in cursor.fetchall():
                    accuracy_stats[analysis_type] = {
                        "sample_count": count,
                        "average_time": avg_time,
                        "confidence": min(0.9, 0.3 + (count * 0.05))
                    }

                return accuracy_stats

        except Exception as e:
            self.logger.error(f"Failed to get accuracy stats: {e}")
            return {}

    def estimate_phase_times(
        self,
        analysis_type: str,
        total_estimated_time: float
    ) -> Dict[str, float]:
        """Estimate time breakdown by analysis phase"""

        # Phase distribution patterns by analysis type
        phase_patterns = {
            "iterative": {
                "initialization": 0.1,
                "topic_analysis": 0.6,
                "connections": 0.2,
                "synthesis": 0.1
            },
            "recursive": {
                "initialization": 0.15,
                "topic_analysis": 0.5,
                "connections": 0.25,
                "synthesis": 0.1
            },
            "comparative": {
                "initialization": 0.1,
                "topic_analysis": 0.4,
                "connections": 0.3,
                "synthesis": 0.2
            },
            "swot": {
                "initialization": 0.15,
                "topic_analysis": 0.45,
                "connections": 0.2,
                "synthesis": 0.2
            },
            "temporal": {
                "initialization": 0.2,
                "topic_analysis": 0.4,
                "connections": 0.25,
                "synthesis": 0.15
            }
        }

        pattern = phase_patterns.get(analysis_type, phase_patterns["iterative"])

        return {
            phase: total_estimated_time * ratio
            for phase, ratio in pattern.items()
        }
