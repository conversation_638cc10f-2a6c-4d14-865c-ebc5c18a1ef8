"""
Demo script to showcase the enhanced ETA progress dialog
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import random
import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.progress_dialog import ProgressDialog, AnalysisProgressManager

class ETADemo:
    """Demo application for ETA progress dialog"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ETA Progress Dialog Demo")
        self.root.geometry("400x300")
        
        # Create demo UI
        self.setup_ui()
        
        # Progress manager
        self.progress_manager = AnalysisProgressManager(self.root)
    
    def setup_ui(self):
        """Set up the demo UI"""
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text="ETA Progress Dialog Demo",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Description
        desc_label = ttk.Label(
            main_frame,
            text="Click the buttons below to test different analysis types\nwith intelligent ETA prediction:",
            justify=tk.CENTER
        )
        desc_label.pack(pady=(0, 20))
        
        # Demo buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # Iterative analysis demo
        ttk.Button(
            button_frame,
            text="🔄 Iterative Analysis (5 topics)",
            command=lambda: self.demo_analysis("iterative", 5),
            width=30
        ).pack(pady=5)
        
        # Recursive analysis demo
        ttk.Button(
            button_frame,
            text="🌳 Recursive Analysis (3 levels)",
            command=lambda: self.demo_analysis("recursive", 3),
            width=30
        ).pack(pady=5)
        
        # SWOT analysis demo
        ttk.Button(
            button_frame,
            text="📊 SWOT Analysis (4 categories)",
            command=lambda: self.demo_analysis("swot", 4),
            width=30
        ).pack(pady=5)
        
        # Comparative analysis demo
        ttk.Button(
            button_frame,
            text="⚖️ Comparative Analysis (6 topics)",
            command=lambda: self.demo_analysis("comparative", 6),
            width=30
        ).pack(pady=5)
        
        # Temporal analysis demo
        ttk.Button(
            button_frame,
            text="⏰ Temporal Analysis (4 phases)",
            command=lambda: self.demo_analysis("temporal", 4),
            width=30
        ).pack(pady=5)
        
        # Status label
        self.status_label = ttk.Label(
            main_frame,
            text="Ready to demo ETA prediction",
            font=('Arial', 10, 'italic')
        )
        self.status_label.pack(pady=(20, 0))
    
    def demo_analysis(self, analysis_type: str, topic_count: int):
        """Demo an analysis with ETA prediction"""
        
        # Prepare analysis parameters
        analysis_params = {
            "type": analysis_type,
            "model": "llama2",
            "topic_count": topic_count,
            "temperature": 0.7,
            "max_depth": 3 if analysis_type == "recursive" else 1,
            "parallel_processing": False,
            "enable_caching": True,
            "main_topic": f"Demo {analysis_type.title()} Analysis"
        }
        
        # Get appropriate steps
        steps = self.get_demo_steps(analysis_type)
        
        # Update status
        self.status_label.config(text=f"Starting {analysis_type} analysis demo...")
        
        # Start demo analysis
        self.progress_manager.start_analysis(
            lambda progress_callback, is_cancelled: self.simulate_analysis(
                analysis_type, topic_count, progress_callback, is_cancelled
            ),
            steps=steps,
            title=f"{analysis_type.title()} Analysis Demo",
            analysis_params=analysis_params
        )
    
    def get_demo_steps(self, analysis_type: str) -> list:
        """Get demo steps for different analysis types"""
        step_configs = {
            "iterative": [
                "Initializing iterative framework",
                "Analyzing topics sequentially",
                "Finding connections",
                "Creating synthesis"
            ],
            "recursive": [
                "Initializing recursive framework",
                "Analyzing main topic",
                "Generating sub-topics",
                "Building hierarchy",
                "Final synthesis"
            ],
            "swot": [
                "Setting up SWOT framework",
                "Identifying strengths",
                "Identifying weaknesses",
                "Identifying opportunities",
                "Identifying threats",
                "Strategic synthesis"
            ],
            "comparative": [
                "Setting up comparison framework",
                "Analyzing individual topics",
                "Performing comparisons",
                "Scoring and ranking",
                "Generating recommendations"
            ],
            "temporal": [
                "Setting up temporal framework",
                "Analyzing historical context",
                "Identifying current trends",
                "Generating predictions",
                "Creating timeline"
            ]
        }
        
        return step_configs.get(analysis_type, step_configs["iterative"])
    
    def simulate_analysis(self, analysis_type: str, topic_count: int, progress_callback, is_cancelled):
        """Simulate an analysis with realistic timing"""
        
        steps = self.get_demo_steps(analysis_type)
        
        # Simulate different timing patterns for different analysis types
        timing_patterns = {
            "iterative": [2, 8, 3, 2],  # Quick start, long middle, quick end
            "recursive": [3, 4, 6, 4, 3],  # Gradual increase then decrease
            "swot": [2, 3, 3, 4, 4, 4],  # Increasing complexity
            "comparative": [3, 6, 4, 3, 2],  # Peak in middle
            "temporal": [4, 5, 4, 6, 3]  # Variable timing
        }
        
        base_times = timing_patterns.get(analysis_type, [3, 5, 3, 2])
        
        # Adjust timing based on topic count
        time_multiplier = 1 + (topic_count - 3) * 0.2
        
        try:
            for i, (step_name, base_time) in enumerate(zip(steps, base_times)):
                if is_cancelled():
                    return {"error": "Analysis cancelled by user"}
                
                # Calculate step time with some randomness
                step_time = base_time * time_multiplier * random.uniform(0.8, 1.2)
                
                # Update progress at start of step
                progress_callback(
                    i, 
                    f"{step_name}...",
                    f"Processing step {i+1}/{len(steps)}"
                )
                
                # Simulate work with sub-progress updates
                substeps = max(3, int(step_time))
                for j in range(substeps):
                    if is_cancelled():
                        return {"error": "Analysis cancelled by user"}
                    
                    # Sub-step progress
                    phase_progress = (j / substeps) * 100
                    progress_callback(
                        i + (j / substeps),
                        f"{step_name}...",
                        f"Sub-task {j+1}/{substeps}",
                        phase_progress
                    )
                    
                    time.sleep(step_time / substeps)
            
            # Complete
            progress_callback(
                len(steps),
                "Analysis complete!",
                f"Successfully completed {analysis_type} analysis"
            )
            
            # Update main UI status
            self.root.after(0, lambda: self.status_label.config(
                text=f"✅ {analysis_type.title()} analysis demo completed!"
            ))
            
            return {
                "type": analysis_type,
                "topic_count": topic_count,
                "status": "completed",
                "demo": True
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def run(self):
        """Run the demo application"""
        print("🚀 Starting ETA Progress Dialog Demo...")
        print("   Click the buttons to test different analysis types")
        print("   Each demo shows intelligent ETA prediction in action")
        
        self.root.mainloop()

if __name__ == "__main__":
    demo = ETADemo()
    demo.run()
